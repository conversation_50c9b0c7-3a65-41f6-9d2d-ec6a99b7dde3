{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 2.0, "eval_steps": 500, "global_step": 6, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4, "grad_norm": 0.33191677927970886, "learning_rate": 0.0, "loss": 1.9853, "step": 1}, {"epoch": 0.8, "grad_norm": 0.4062306880950928, "learning_rate": 2.0000000000000003e-06, "loss": 2.0713, "step": 2}, {"epoch": 1.0, "grad_norm": 0.9461525678634644, "learning_rate": 4.000000000000001e-06, "loss": 2.6032, "step": 3}, {"epoch": 1.4, "grad_norm": 0.34628257155418396, "learning_rate": 6e-06, "loss": 2.0185, "step": 4}, {"epoch": 1.8, "grad_norm": 0.46443966031074524, "learning_rate": 8.000000000000001e-06, "loss": 2.0637, "step": 5}, {"epoch": 2.0, "grad_norm": 0.5346542596817017, "learning_rate": 1e-05, "loss": 2.2198, "step": 6}], "logging_steps": 1, "max_steps": 9, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 270900045545472.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}