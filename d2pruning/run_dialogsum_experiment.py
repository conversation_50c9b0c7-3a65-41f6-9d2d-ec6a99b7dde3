import os
import sys
import json
import time
import logging
import argparse
import subprocess
from pathlib import Path
from typing import List, Dict, Any

from wandb_logger import setup_wandb_logging


logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


class DialogSumExperiment:
    def __init__(self, base_dir: str = "./", enable_wandb: bool = True):
        self.base_dir = Path(base_dir)
        self.cache_dir = self.base_dir / "cache"
        self.results_dir = self.base_dir / "results"
        self.models_dir = self.base_dir / "finetuned_models"
        self.eval_dir = self.base_dir / "evaluation_results"
        self.enable_wandb = enable_wandb
        self.wandb_logger = None

        for dir_path in [self.cache_dir, self.results_dir, self.models_dir, self.eval_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def run_data_selection(self, selection_method: str, num_samples: int,
                          embedding_model: str = "meta-llama/Llama-2-7b-hf",
                          force_regenerate: bool = False) -> str:
        logger.info(f"Running data selection with method: {selection_method}")
        
        results_file = self.results_dir / f"selection_results_{selection_method}_{num_samples}.json"
        

        should_regenerate = force_regenerate
        if results_file.exists() and not force_regenerate:
            try:
                with open(results_file, 'r') as f:
                    existing_results = json.load(f)
                existing_embedding_model = existing_results.get('embedding_model', 'unknown')
                if existing_embedding_model != embedding_model:
                    logger.info(f"Embedding model changed from {existing_embedding_model} to {embedding_model}")
                    should_regenerate = True
            except Exception as e:
                logger.warning(f"Could not read existing results: {e}")
                should_regenerate = True

        if results_file.exists() and not should_regenerate:
            logger.info(f"Selection results already exist: {results_file}")
            return str(results_file)
        elif should_regenerate:
            logger.info(f"Regenerating selection results with new embedding model: {embedding_model}")
        

        cmd = [
            sys.executable, "dialogsum_selection.py",
            "--num_samples", str(num_samples),
            "--selection_method", selection_method,
            "--embedding_model", embedding_model,
            "--cache_dir", str(self.cache_dir),
            "--output_dir", str(self.results_dir)
        ]

        if self.enable_wandb:
            cmd.extend(["--enable_wandb", "--wandb_run_name", f"selection_{selection_method}_{num_samples}"])
        
        logger.info(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Data selection failed: {result.stderr}")
            raise RuntimeError(f"Data selection failed for {selection_method}")
        
        logger.info(f"Data selection completed: {results_file}")
        return str(results_file)
    
    def run_finetuning(self, selection_results_file: str, model_name: str = "meta-llama/Llama-2-7b-hf",
                      format_type: str = "instruction", num_epochs: int = 3) -> str:
        logger.info(f"Running fine-tuning for {selection_results_file}")
        
        with open(selection_results_file, 'r') as f:
            selection_data = json.load(f)
        
        selection_method = selection_data['selection_method']
        num_samples = selection_data['num_samples']
        
        model_dir = self.models_dir / f"{selection_method}_{num_samples}samples_{format_type}"
        training_results_file = model_dir / "training_results.json"
        

        if training_results_file.exists():
            logger.info(f"Fine-tuned model already exists: {model_dir}")
            return str(model_dir)
        
        cmd = [
            sys.executable, "finetune_llama.py",
            "--selection_results", selection_results_file,
            "--model_name", model_name,
            "--format_type", format_type,
            "--num_epochs", str(num_epochs),
            "--output_dir", str(self.models_dir),
            "--cache_dir", str(self.cache_dir)
        ]

        if self.enable_wandb:
            cmd.extend(["--enable_wandb", "--wandb_run_name", f"finetune_{selection_method}_{num_samples}"])
        
        logger.info(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Fine-tuning failed: {result.stderr}")
            raise RuntimeError(f"Fine-tuning failed for {selection_method}")
        
        logger.info(f"Fine-tuning completed: {model_dir}")
        return str(model_dir)
    
    def run_evaluation(self, model_dirs: List[str], base_model: str = "meta-llama/Llama-2-7b-hf",
                      max_test_samples: int = 500) -> str:
        logger.info("Running evaluation on all models")
        
        comparison_file = self.eval_dir / "comparison_summary.json"
        if comparison_file.exists():
            logger.info(f"Evaluation results already exist: {comparison_file}")
            return str(self.eval_dir)
        
        cmd = [
            sys.executable, "evaluate_summaries.py",
            "--model_dirs"] + model_dirs + [
            "--base_model", base_model,
            "--max_test_samples", str(max_test_samples),
            "--output_dir", str(self.eval_dir),
            "--cache_dir", str(self.cache_dir)
        ]

        if self.enable_wandb:
            cmd.extend(["--enable_wandb", "--wandb_run_name", f"eval_{len(model_dirs)}models"])
            wandb_api_key = os.getenv("WANDB_API_KEY")
            if wandb_api_key:
                cmd.extend(["--wandb_api_key", wandb_api_key])
        
        logger.info(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Evaluation failed: {result.stderr}")
            raise RuntimeError("Evaluation failed")
        
        logger.info(f"Evaluation completed: {self.eval_dir}")
        return str(self.eval_dir)
    
    def run_complete_experiment(self, selection_methods: List[str], num_samples: int = 10,
                               model_name: str = "meta-llama/Llama-2-7b-hf",
                               format_type: str = "instruction", num_epochs: int = 3,
                               max_test_samples: int = 500,
                               force_regenerate: bool = False) -> Dict[str, Any]:
        logger.info("Starting complete DialogSum experiment")
        logger.info(f"Selection methods: {selection_methods}")
        logger.info(f"Number of samples: {num_samples}")
        logger.info(f"Model: {model_name}")
        logger.info(f"Format: {format_type}")
        logger.info(f"Epochs: {num_epochs}")

        experiment_start_time = time.time()

        if self.enable_wandb:
            experiment_config = {
                "selection_methods": selection_methods,
                "num_samples": num_samples,
                "model_name": model_name,
                "format_type": format_type,
                "num_epochs": num_epochs,
                "max_test_samples": max_test_samples,
                "experiment_type": "dialogsum_data_selection"
            }

            experiment_name = f"dialogsum_{num_samples}samples_{format_type}_{time.strftime('%Y%m%d_%H%M%S')}"
            self.wandb_logger = setup_wandb_logging(experiment_config, experiment_name)
        
        # Step 1: Data Selection
        logger.info("="*60)
        logger.info("STEP 1: DATA SELECTION")
        logger.info("="*60)
        
        selection_results_files = []
        for method in selection_methods:
            try:
                results_file = self.run_data_selection(method, num_samples,
                                                      force_regenerate=force_regenerate)
                selection_results_files.append(results_file)
            except Exception as e:
                logger.error(f"Failed to run selection for {method}: {e}")
                continue
        
        if not selection_results_files:
            raise RuntimeError("No selection methods completed successfully")
        
        # Step 2: Fine-tuning
        logger.info("="*60)
        logger.info("STEP 2: FINE-TUNING")
        logger.info("="*60)
        
        model_dirs = []
        for results_file in selection_results_files:
            try:
                model_dir = self.run_finetuning(
                    results_file, model_name, format_type, num_epochs
                )
                model_dirs.append(model_dir)
            except Exception as e:
                logger.error(f"Failed to fine-tune model for {results_file}: {e}")
                continue
        
        if not model_dirs:
            raise RuntimeError("No models were fine-tuned successfully")
        
        # Step 3: Evaluation
        logger.info("="*60)
        logger.info("STEP 3: EVALUATION")
        logger.info("="*60)
        
        try:
            eval_dir = self.run_evaluation(model_dirs, model_name, max_test_samples)
        except Exception as e:
            logger.error(f"Failed to run evaluation: {e}")
            raise
        
        experiment_time = time.time() - experiment_start_time

        comparison_file = Path(eval_dir) / "comparison_summary.json"
        with open(comparison_file, 'r') as f:
            comparison_results = json.load(f)

        if self.enable_wandb and self.wandb_logger:
            self.wandb_logger.log_comparison_summary(comparison_results)
            self.wandb_logger.log_experiment_completion(
                experiment_time,
                list(comparison_results.keys()),
                []  # No failed methods if we got here
            )


            csv_path = self.base_dir / "experiment_results.csv"
            self.wandb_logger.create_summary_csv(comparison_results, str(csv_path))


            artifacts = {
                "experiment_summary": str(self.base_dir / "experiment_summary.json"),
                "comparison_summary": str(comparison_file),
                "results_csv": str(csv_path)
            }
            self.wandb_logger.save_artifacts(artifacts)


        experiment_summary = {
            "experiment_config": {
                "selection_methods": selection_methods,
                "num_samples": num_samples,
                "model_name": model_name,
                "format_type": format_type,
                "num_epochs": num_epochs,
                "max_test_samples": max_test_samples
            },
            "experiment_time": experiment_time,
            "results": comparison_results
        }


        summary_file = self.base_dir / "experiment_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(experiment_summary, f, indent=2)


        if self.enable_wandb and self.wandb_logger:
            self.wandb_logger.finish_experiment()

        logger.info("="*60)
        logger.info("EXPERIMENT COMPLETED")
        logger.info("="*60)
        logger.info(f"Total experiment time: {experiment_time:.2f} seconds")
        logger.info(f"Results saved to: {summary_file}")

        return experiment_summary


def print_final_summary(experiment_summary: Dict[str, Any]):
    print("\n" + "="*80)
    print("DIALOGSUM DATA SELECTION EXPERIMENT SUMMARY")
    print("="*80)
    
    config = experiment_summary["experiment_config"]
    print(f"Configuration:")
    print(f"  Selection Methods: {', '.join(config['selection_methods'])}")
    print(f"  Training Samples: {config['num_samples']}")
    print(f"  Model: {config['model_name']}")
    print(f"  Format: {config['format_type']}")
    print(f"  Epochs: {config['num_epochs']}")
    print(f"  Test Samples: {config['max_test_samples']}")
    print(f"  Total Experiment Time: {experiment_summary['experiment_time']:.2f} seconds")
    
    print(f"\nResults:")
    results = experiment_summary["results"]
    
    # Sort by ROUGE-1 score for comparison
    sorted_results = sorted(results.items(), key=lambda x: x[1]['rougeL'], reverse=True)
    
    print(f"{'Method':<15} {'Samples':<8} {'ROUGE-1':<8} {'ROUGE-2':<8} {'ROUGE-L':<8} {'Train Time':<12} {'Gen Time':<10}")
    print("-" * 80)
    
    for model_key, summary in sorted_results:
        method = summary['selection_method']
        samples = summary['num_samples']
        rouge1 = summary['rouge1']
        rouge2 = summary['rouge2']
        rougeL = summary['rougeL']
        train_time = summary['training_time']
        gen_time = summary['generation_time']
        
        print(f"{method:<15} {samples:<8} {rouge1:<8.4f} {rouge2:<8.4f} {rougeL:<8.4f} {train_time:<12.2f} {gen_time:<10.2f}")
    
    # Find best method
    best_method = sorted_results[0]
    print(f"\nBest performing method: {best_method[1]['selection_method']} (ROUGE-1: {best_method[1]['rouge1']:.4f})")
    
    print("="*80)


def main():
    parser = argparse.ArgumentParser(description="DialogSum Data Selection Experiment")
    parser.add_argument("--selection_methods", type=str, nargs='+', 
                       default=["random", "moderate", "k_center"],
                       choices=["random", "moderate", "k_center", "diversity"],
                       help="Selection methods to compare")
    parser.add_argument("--num_samples", type=int, default=10,
                       help="Number of samples to select for training")
    parser.add_argument("--model_name", type=str, default="meta-llama/Llama-2-7b-hf",
                       help="LLaMA model to fine-tune")
    parser.add_argument("--format_type", type=str, default="alpaca",
                       choices=["instruction", "chat", "alpaca", "vicuna"],
                       help="Data formatting approach")
    parser.add_argument("--num_epochs", type=int, default=3,
                       help="Number of training epochs")
    parser.add_argument("--max_test_samples", type=int, default=500,
                       help="Maximum test samples for evaluation (default: 500 for faster results)")
    parser.add_argument("--base_dir", type=str, default="./",
                       help="Base directory for the experiment")
    parser.add_argument("--enable_wandb", action="store_true", default=True,
                       help="Enable Weights & Biases logging (default: True)")
    parser.add_argument("--disable_wandb", action="store_true", default=False,
                       help="Disable Weights & Biases logging")
    parser.add_argument("--force_regenerate", action="store_true", default=False,
                       help="Force regeneration of selection results (ignore cache)")
    parser.add_argument("--clear_cache", action="store_true", default=False,
                       help="Clear all cached results before running")

    args = parser.parse_args()

    if args.disable_wandb:
        args.enable_wandb = False
    
    experiment = DialogSumExperiment(base_dir=args.base_dir, enable_wandb=args.enable_wandb)

    if args.clear_cache:
        import shutil
        cache_dir = Path(args.base_dir) / "cache"
        results_dir = Path(args.base_dir) / "results"

        if cache_dir.exists():
            logger.info(f"Clearing cache directory: {cache_dir}")
            shutil.rmtree(cache_dir)

        if results_dir.exists():
            logger.info(f"Clearing results directory: {results_dir}")
            shutil.rmtree(results_dir)
    
    try:
        experiment_summary = experiment.run_complete_experiment(
            selection_methods=args.selection_methods,
            num_samples=args.num_samples,
            model_name=args.model_name,
            format_type=args.format_type,
            num_epochs=args.num_epochs,
            max_test_samples=args.max_test_samples,
            force_regenerate=args.force_regenerate
        )
        
        print_final_summary(experiment_summary)
        
    except Exception as e:
        logger.error(f"Experiment failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
