2025-07-23 12:32:06,183 INFO    MainThread:494901 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-23 12:32:06,183 INFO    MainThread:494901 [wandb_setup.py:_flush():80] Configure stats pid to 494901
2025-07-23 12:32:06,183 INFO    MainThread:494901 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-23 12:32:06,184 INFO    MainThread:494901 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-23 12:32:06,184 INFO    MainThread:494901 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-23 12:32:06,184 INFO    MainThread:494901 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250723_123206-fn44wwho/logs/debug.log
2025-07-23 12:32:06,184 INFO    MainThread:494901 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250723_123206-fn44wwho/logs/debug-internal.log
2025-07-23 12:32:06,184 INFO    MainThread:494901 [wandb_init.py:init():830] calling init triggers
2025-07-23 12:32:06,184 INFO    MainThread:494901 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_method': 'random', 'num_samples': 10, 'embedding_model': 'all-MiniLM-L6-v2', 'seed': 42, 'task': 'data_selection', '_wandb': {}}
2025-07-23 12:32:06,184 INFO    MainThread:494901 [wandb_init.py:init():871] starting backend
2025-07-23 12:32:06,197 INFO    MainThread:494901 [wandb_init.py:init():874] sending inform_init request
2025-07-23 12:32:06,203 INFO    MainThread:494901 [wandb_init.py:init():882] backend started and connected
2025-07-23 12:32:06,206 INFO    MainThread:494901 [wandb_init.py:init():953] updated telemetry
2025-07-23 12:32:06,214 INFO    MainThread:494901 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-23 12:32:06,942 INFO    MainThread:494901 [wandb_init.py:init():1029] starting run threads in backend
2025-07-23 12:32:07,220 INFO    MainThread:494901 [wandb_run.py:_console_start():2458] atexit reg
2025-07-23 12:32:07,221 INFO    MainThread:494901 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-23 12:32:07,221 INFO    MainThread:494901 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-23 12:32:07,221 INFO    MainThread:494901 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-23 12:32:07,223 INFO    MainThread:494901 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-23 12:32:43,712 INFO    MainThread:494901 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/fn44wwho
2025-07-23 12:32:43,713 INFO    MainThread:494901 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-23 12:32:43,713 INFO    MainThread:494901 [wandb_run.py:_restore():2405] restore
2025-07-23 12:32:43,713 INFO    MainThread:494901 [wandb_run.py:_restore():2411] restore done
2025-07-23 12:32:45,666 INFO    MainThread:494901 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-23 12:32:45,667 INFO    MainThread:494901 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-23 12:32:45,668 INFO    MainThread:494901 [wandb_run.py:_footer_sync_info():3864] logging synced files
