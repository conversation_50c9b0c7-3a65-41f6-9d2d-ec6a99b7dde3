#!/usr/bin/env python3
"""
Fix D2Pruning Selection

This script attempts to perform d2pruning selection with detailed error handling
and debugging to track down the issue with the selection process.
"""

import os
import sys
import json
import logging
import numpy as np
import torch
from pathlib import Path
from tqdm import tqdm
import traceback

# Add d2pruning to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'd2pruning'))
# Add core to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'd2pruning/core'))

# Import required modules
from dialogsum_selection import DialogSumProcessor, EmbeddingExtractor
import dialogsum_selection
from data import sampling

# Set up logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

def main():
    # Define paths
    training_dynamics_path = Path("d2pruning_experiment/training_dynamics/training_dynamics_scores.npy")
    output_dir = Path("d2pruning_experiment/selection")
    cache_dir = Path("d2pruning/cache")
    num_samples = 10
    seed = 42
    
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Set random seeds
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    logger.info(f"Loading training dynamics from {training_dynamics_path}")
    
    # Check if the training dynamics file exists
    if not training_dynamics_path.exists():
        logger.error(f"Training dynamics file not found at {training_dynamics_path}")
        return
    
    try:
        difficulty_scores = np.load(training_dynamics_path)
        logger.info(f"Loaded difficulty scores with shape {difficulty_scores.shape}")
        
        # Check if the difficulty scores are valid
        if np.isnan(difficulty_scores).any() or np.isinf(difficulty_scores).any():
            logger.error("Difficulty scores contain NaN or Inf values")
            logger.info(f"Replacing NaN/Inf values with zeros")
            difficulty_scores = np.nan_to_num(difficulty_scores, nan=0.0, posinf=1.0, neginf=0.0)
        
        # Normalize scores
        min_val = difficulty_scores.min()
        max_val = difficulty_scores.max()
        logger.info(f"Difficulty scores range: [{min_val}, {max_val}]")
        
        difficulty_scores = (difficulty_scores - min_val) / (max_val - min_val + 1e-8)
        logger.info(f"Normalized difficulty scores range: [{difficulty_scores.min()}, {difficulty_scores.max()}]")
        
        # Load DialogSum dataset
        logger.info("Loading DialogSum dataset")
        processor = DialogSumProcessor(cache_dir=str(cache_dir))
        train_dataset = processor.load_dialogsum("train")
        train_data = processor.preprocess_for_selection(train_dataset)
        
        # Get embeddings - use the same model as the original script
        logger.info("Loading embeddings")
        extractor = EmbeddingExtractor(model_name="all-mpnet-base-v2", cache_dir=str(cache_dir))
        embeddings = extractor.extract_embeddings(
            train_data['combined_texts'],
            cache_key="dialogsum_train_combined"
        )
        logger.info(f"Loaded embeddings with shape {embeddings.shape}")
        
        # Check that the number of embeddings matches the difficulty scores
        if len(embeddings) != len(difficulty_scores):
            logger.error(f"Mismatch: {len(embeddings)} embeddings vs {len(difficulty_scores)} difficulty scores")
            # Truncate to the smaller of the two
            min_len = min(len(embeddings), len(difficulty_scores))
            logger.info(f"Truncating both to {min_len} elements")
            embeddings = embeddings[:min_len]
            difficulty_scores = difficulty_scores[:min_len]
        
        # Create D2Args class
        class D2Args:
            graph_mode = "product"
            graph_sampling_mode = "absolute"
            n_neighbor = 10
            precomputed_dists = None
            precomputed_neighbors = None
        
        logger.info("Creating GraphDensitySampler")
        try:
            sampler = sampling.GraphDensitySampler(
                X=embeddings,
                y=None,
                seed=seed,
                importance_scores=torch.from_numpy(difficulty_scores).float(),
                args=D2Args()
            )
            
            logger.info(f"Selecting {num_samples} samples using d2pruning")
            selected_indices = sampler.select_batch_(N=num_samples)
            
            logger.info(f"Selected indices: {selected_indices}")
            
            # Create results dictionary
            results = {
                'selection_method': "d2pruning",
                'num_samples': num_samples,
                'selected_indices': selected_indices.tolist(),
                'selection_time': 0.0,  # Not measuring time here
                'embedding_model': "all-mpnet-base-v2",
                'train_dataset_size': len(train_data['dialogues']),
                'selected_dialogues': [train_data['dialogues'][i] for i in selected_indices],
                'selected_summaries': [train_data['summaries'][i] for i in selected_indices],
                'metadata': train_data['metadata']
            }
            
            # Save results to output directory
            results_file = output_dir / f"selection_results_d2pruning_{num_samples}.json"
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
                
            logger.info(f"Results saved to {results_file}")
            
        except Exception as e:
            logger.error(f"Error during d2pruning selection: {e}")
            logger.error(traceback.format_exc())
            return
        
    except Exception as e:
        logger.error(f"Error loading training dynamics: {e}")
        logger.error(traceback.format_exc())
        return

if __name__ == "__main__":
    main()
