07/25/2025 02:18:15 - INFO - __main__ - Loading model from d2pruning/finetuned_models/k_center_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 02:18:15 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████| 2/2 [00:26<00:00, 13.18s/it]
07/25/2025 02:18:45 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 02:18:45 - INFO - __main__ - Starting summary generation...
Generating Summaries:   0%|                                                                  | 0/2 [00:00<?, ?it/s]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
Generating Summaries: 100%|██████████████████████████████████████████████████████████| 2/2 [00:58<00:00, 29.29s/it]
07/25/2025 02:19:44 - INFO - __main__ - Generated 5 summaries.
07/25/2025 02:19:44 - INFO - __main__ - Computing and logging metrics...
Calculating ROUGE:   0%|                                                                     | 0/1 [00:00<?, ?it/s]07/25/2025 02:19:44 - INFO - absl - Using default tokenizer.
07/25/2025 02:19:44 - INFO - absl - Using default tokenizer.
Calculating ROUGE:   0%|                                                                     | 0/1 [00:00<?, ?it/s]
07/25/2025 02:19:44 - ERROR - __main__ - Failed to evaluate model k_center_10samples_chat: 'numpy.float64' object has no attribute 'mid'
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 276, in main
    rouge_scores = evaluator.compute_and_log_metrics(predictions, references)
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 184, in compute_and_log_metrics
    "eval/rouge1": agg_scores["rouge1"].mid.fmeasure,
AttributeError: 'numpy.float64' object has no attribute 'mid'. Did you mean: 'min'?
