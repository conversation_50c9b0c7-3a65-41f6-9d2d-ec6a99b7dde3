07/25/2025 11:59:40 - INFO - __main__ - Logging to ../logs/finetune_20250725_115940.log
07/25/2025 11:59:40 - INFO - __main__ - Loading selection results from d2pruning/results/selection_results_random_10.json
wandb: Currently logged in as: joshua<PERSON>shu<PERSON> (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: WARNING Using a boolean value for 'reinit' is deprecated. Use 'return_previous' or 'finish_previous' instead.
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_115940-i8xwdg6q
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_random_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/i8xwdg6q
07/25/2025 11:59:42 - INFO - wandb_logger - Initialized W&B run: finetune_random_10
07/25/2025 11:59:42 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/i8xwdg6q
07/25/2025 11:59:42 - INFO - __main__ - W&B logging initialized with run name: finetune_random_10
07/25/2025 11:59:42 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 11:59:45 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]