07/24/2025 12:00:31 - INFO - wandb_logger - Initialized W&B run: finetune_k_center_10
07/24/2025 12:00:31 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/gx3y3d17
07/24/2025 12:00:31 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 12:00:34 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [01:17<00:00, 38.68s/it]
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484
07/24/2025 12:01:53 - INFO - __main__ - Preparing training dataset...
Map: 100%|██████████| 10/10 [00:00<00:00, 17.35 examples/s]
07/24/2025 12:02:18 - INFO - __main__ - Starting fine-tuning...
07/24/2025 12:02:18 - INFO - wandb_logger - Logged training start for k_center to W&B
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.
  0%|          | 0/9 [00:00<?, ?it/s]Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 767, in convert_to_tensors
    tensor = as_tensor(value)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 729, in as_tensor
    return torch.tensor(value)
ValueError: too many dimensions 'str'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 241, in train
    trainer.train()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
    return inner_training_loop(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2502, in _inner_training_loop
    batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 5300, in get_batch_samples
    batch_samples.append(next(epoch_iterator))
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
    current_batch = next(dataloader_iter)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
    data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
    return self.collate_fn(data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 46, in __call__
    return self.torch_call(features)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 1014, in torch_call
    batch = pad_without_fast_tokenizer_warning(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 67, in pad_without_fast_tokenizer_warning
    padded = tokenizer.pad(*pad_args, **pad_kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3374, in pad
    return BatchEncoding(batch_outputs, tensor_type=return_tensors)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 240, in __init__
    self.convert_to_tensors(tensor_type=tensor_type, prepend_batch_axis=prepend_batch_axis)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 783, in convert_to_tensors
    raise ValueError(
ValueError: Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' 'truncation=True' to have batched tensors with the same length. Perhaps your features (`text` in this case) have excessive nesting (inputs type `list` where type `int` is expected).
  0%|          | 0/9 [00:00<?, ?it/s]
