07/24/2025 11:34:56 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_instruction_20250724_113452
07/24/2025 11:34:56 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/f6e0o7jm
07/24/2025 11:34:56 - INFO - __main__ - ============================================================
07/24/2025 11:34:56 - INFO - __main__ - STEP 1: DATA SELECTION
07/24/2025 11:34:56 - INFO - __main__ - ============================================================
07/24/2025 11:34:56 - INFO - __main__ - Running data selection with method: random
07/24/2025 11:34:56 - INFO - __main__ - Selection results already exist: results/selection_results_random_10.json
07/24/2025 11:34:56 - INFO - __main__ - Running data selection with method: moderate
07/24/2025 11:34:56 - INFO - __main__ - Selection results already exist: results/selection_results_moderate_10.json
07/24/2025 11:34:56 - INFO - __main__ - Running data selection with method: k_center
07/24/2025 11:34:56 - INFO - __main__ - Selection results already exist: results/selection_results_k_center_10.json
07/24/2025 11:34:56 - INFO - __main__ - ============================================================
07/24/2025 11:34:56 - INFO - __main__ - STEP 2: FINE-TUNING
07/24/2025 11:34:56 - INFO - __main__ - ============================================================
07/24/2025 11:34:56 - INFO - __main__ - Running fine-tuning for results/selection_results_random_10.json
07/24/2025 11:34:56 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_random_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_random_10
07/24/2025 11:46:24 - ERROR - __main__ - Fine-tuning failed: 07/24/2025 11:35:07 - INFO - __main__ - Loading selection results from results/selection_results_random_10.json
07/24/2025 11:35:07 - INFO - __main__ - Loaded 10 selected samples using random method
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_113509-x09ubqjk
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_random_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/x09ubqjk
07/24/2025 11:35:10 - INFO - wandb_logger - Initialized W&B run: finetune_random_10
07/24/2025 11:35:10 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/x09ubqjk
07/24/2025 11:35:10 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf

Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]
Fetching 2 files:  50%|█████     | 1/2 [09:01<09:01, 541.78s/it]
Fetching 2 files: 100%|██████████| 2/2 [09:01<00:00, 270.89s/it]
07/24/2025 11:44:22 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [01:03<01:03, 63.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:27<00:00, 40.46s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:27<00:00, 43.84s/it]
07/24/2025 11:45:52 - INFO - __main__ - Preparing training dataset...

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 29.48 examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 27.95 examples/s]
07/24/2025 11:46:17 - INFO - __main__ - Starting fine-tuning...
07/24/2025 11:46:17 - INFO - wandb_logger - Logged training start for random to W&B
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 206, in train
    training_args = TrainingArguments(
TypeError: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'

07/24/2025 11:46:24 - ERROR - __main__ - Failed to fine-tune model for results/selection_results_random_10.json: Fine-tuning failed for random
07/24/2025 11:46:25 - INFO - __main__ - Running fine-tuning for results/selection_results_moderate_10.json
07/24/2025 11:46:25 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_moderate_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_moderate_10
07/24/2025 11:48:48 - ERROR - __main__ - Fine-tuning failed: 07/24/2025 11:46:51 - INFO - __main__ - Loading selection results from results/selection_results_moderate_10.json
07/24/2025 11:46:51 - INFO - __main__ - Loaded 10 selected samples using moderate method
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_114654-ts1egzio
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_moderate_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/ts1egzio
07/24/2025 11:46:56 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 11:46:56 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/ts1egzio
07/24/2025 11:46:56 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 11:46:59 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [01:06<01:06, 66.66s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:16<00:00, 33.44s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:16<00:00, 38.43s/it]
07/24/2025 11:48:17 - INFO - __main__ - Preparing training dataset...

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 25.31 examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 24.16 examples/s]
07/24/2025 11:48:41 - INFO - __main__ - Starting fine-tuning...
07/24/2025 11:48:41 - INFO - wandb_logger - Logged training start for moderate to W&B
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 206, in train
    training_args = TrainingArguments(
TypeError: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'

07/24/2025 11:48:48 - ERROR - __main__ - Failed to fine-tune model for results/selection_results_moderate_10.json: Fine-tuning failed for moderate
07/24/2025 11:48:48 - INFO - __main__ - Running fine-tuning for results/selection_results_k_center_10.json
07/24/2025 11:48:48 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_k_center_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_k_center_10
07/24/2025 11:51:09 - ERROR - __main__ - Fine-tuning failed: 07/24/2025 11:49:12 - INFO - __main__ - Loading selection results from results/selection_results_k_center_10.json
07/24/2025 11:49:12 - INFO - __main__ - Loaded 10 selected samples using k_center method
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_114915-p2ej0hkb
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_k_center_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/p2ej0hkb
07/24/2025 11:49:18 - INFO - wandb_logger - Initialized W&B run: finetune_k_center_10
07/24/2025 11:49:18 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/p2ej0hkb
07/24/2025 11:49:18 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 11:49:21 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:55<00:55, 55.53s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:16<00:00, 34.97s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:16<00:00, 38.05s/it]
07/24/2025 11:50:38 - INFO - __main__ - Preparing training dataset...

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 10.30 examples/s]
Map: 100%|██████████| 10/10 [00:01<00:00,  9.72 examples/s]
07/24/2025 11:51:04 - INFO - __main__ - Starting fine-tuning...
07/24/2025 11:51:04 - INFO - wandb_logger - Logged training start for k_center to W&B
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 206, in train
    training_args = TrainingArguments(
TypeError: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'

07/24/2025 11:51:09 - ERROR - __main__ - Failed to fine-tune model for results/selection_results_k_center_10.json: Fine-tuning failed for k_center
07/24/2025 11:51:09 - ERROR - __main__ - Experiment failed: No models were fine-tuned successfully
