07/23/2025 14:38:08 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_instruction_20250723_143806
07/23/2025 14:38:08 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/jk879xjv
07/23/2025 14:38:08 - INFO - __main__ - ============================================================
07/23/2025 14:38:08 - INFO - __main__ - STEP 1: DATA SELECTION
07/23/2025 14:38:08 - INFO - __main__ - ============================================================
07/23/2025 14:38:08 - INFO - __main__ - Running data selection with method: random
07/23/2025 14:38:08 - INFO - __main__ - Selection results already exist: results/selection_results_random_10.json
07/23/2025 14:38:08 - INFO - __main__ - Running data selection with method: moderate
07/23/2025 14:38:08 - INFO - __main__ - Selection results already exist: results/selection_results_moderate_10.json
07/23/2025 14:38:08 - INFO - __main__ - Running data selection with method: k_center
07/23/2025 14:38:08 - INFO - __main__ - Selection results already exist: results/selection_results_k_center_10.json
07/23/2025 14:38:08 - INFO - __main__ - ============================================================
07/23/2025 14:38:08 - INFO - __main__ - STEP 2: FINE-TUNING
07/23/2025 14:38:08 - INFO - __main__ - ============================================================
07/23/2025 14:38:08 - INFO - __main__ - Running fine-tuning for results/selection_results_random_10.json
07/23/2025 14:38:08 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_random_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_random_10
07/23/2025 15:17:18 - ERROR - __main__ - Fine-tuning failed: 07/23/2025 14:38:19 - INFO - __main__ - Loading selection results from results/selection_results_random_10.json
07/23/2025 14:38:19 - INFO - __main__ - Loaded 10 selected samples using random method
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250723_143821-sl4svjqn
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_random_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/sl4svjqn
07/23/2025 14:38:22 - INFO - wandb_logger - Initialized W&B run: finetune_random_10
07/23/2025 14:38:22 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/sl4svjqn
07/23/2025 14:38:22 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.

Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]
07/23/2025 15:17:18 - ERROR - __main__ - Failed to fine-tune model for results/selection_results_random_10.json: Fine-tuning failed for random
07/23/2025 15:17:18 - INFO - __main__ - Running fine-tuning for results/selection_results_moderate_10.json
07/23/2025 15:17:18 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_moderate_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_moderate_10
