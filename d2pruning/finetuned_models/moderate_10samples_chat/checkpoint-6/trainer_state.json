{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 2.0, "eval_steps": 500, "global_step": 6, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4, "grad_norm": 0.5073608756065369, "learning_rate": 0.0, "loss": 2.051, "step": 1}, {"epoch": 0.8, "grad_norm": 0.47231969237327576, "learning_rate": 2.0000000000000003e-06, "loss": 2.052, "step": 2}, {"epoch": 1.0, "grad_norm": 0.4352172017097473, "learning_rate": 4.000000000000001e-06, "loss": 1.9974, "step": 3}, {"epoch": 1.4, "grad_norm": 0.46897220611572266, "learning_rate": 6e-06, "loss": 2.1044, "step": 4}, {"epoch": 1.8, "grad_norm": 0.4345324635505676, "learning_rate": 8.000000000000001e-06, "loss": 2.0206, "step": 5}, {"epoch": 2.0, "grad_norm": 0.560967206954956, "learning_rate": 1e-05, "loss": 1.9174, "step": 6}], "logging_steps": 1, "max_steps": 9, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 229565531553792.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}