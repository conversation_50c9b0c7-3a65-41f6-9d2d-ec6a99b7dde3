2025-07-24 11:49:15,708 INFO    MainThread:2026738 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-24 11:49:15,708 INFO    MainThread:2026738 [wandb_setup.py:_flush():80] Configure stats pid to 2026738
2025-07-24 11:49:15,708 INFO    MainThread:2026738 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-24 11:49:15,709 INFO    MainThread:2026738 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-24 11:49:15,709 INFO    MainThread:2026738 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-24 11:49:15,709 INFO    MainThread:2026738 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_114915-p2ej0hkb/logs/debug.log
2025-07-24 11:49:15,709 INFO    MainThread:2026738 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_114915-p2ej0hkb/logs/debug-internal.log
2025-07-24 11:49:15,709 INFO    MainThread:2026738 [wandb_init.py:init():830] calling init triggers
2025-07-24 11:49:15,709 INFO    MainThread:2026738 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_method': 'k_center', 'num_samples': 10, 'model_name': 'meta-llama/Llama-2-7b-hf', 'format_type': 'instruction', 'num_epochs': 3, 'learning_rate': 0.0002, 'task': 'fine_tuning', '_wandb': {}}
2025-07-24 11:49:15,709 INFO    MainThread:2026738 [wandb_init.py:init():871] starting backend
2025-07-24 11:49:15,740 INFO    MainThread:2026738 [wandb_init.py:init():874] sending inform_init request
2025-07-24 11:49:15,757 INFO    MainThread:2026738 [wandb_init.py:init():882] backend started and connected
2025-07-24 11:49:15,761 INFO    MainThread:2026738 [wandb_init.py:init():953] updated telemetry
2025-07-24 11:49:15,834 INFO    MainThread:2026738 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-24 11:49:16,635 INFO    MainThread:2026738 [wandb_init.py:init():1029] starting run threads in backend
2025-07-24 11:49:18,055 INFO    MainThread:2026738 [wandb_run.py:_console_start():2458] atexit reg
2025-07-24 11:49:18,056 INFO    MainThread:2026738 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-24 11:49:18,056 INFO    MainThread:2026738 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-24 11:49:18,057 INFO    MainThread:2026738 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-24 11:49:18,074 INFO    MainThread:2026738 [wandb_init.py:init():1075] run started, returning control to user process
