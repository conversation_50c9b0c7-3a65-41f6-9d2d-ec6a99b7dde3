#!/usr/bin/env python3
"""
Example script showing how to use the fixed DialogSum evaluation script.

This demonstrates how to evaluate models with proper wandb logging and 
consistent formatting with your existing setup.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_evaluation_example():
    """Run an example evaluation."""
    
    # Example 1: Evaluate a base LLaMA model on DialogSum
    print("="*60)
    print("EXAMPLE 1: Base Model Evaluation")
    print("="*60)
    
    cmd1 = [
        sys.executable, "evaluate_dialogsum_benchmark.py",
        "--model_id", "meta-llama/Llama-2-7b-hf",
        "--dataset_id", "knkarthick/dialogsum",
        "--split_name", "test",
        "--max_samples", "50",  # Small sample for testing
        "--batch_size", "2",
        "--seed", "42",
        "--output_path", "./evaluation_results/base_model",
        "--save_predictions"
    ]
    
    # Add wandb if API key is available
    wandb_key = os.getenv("WANDB_API_KEY")
    if wandb_key:
        cmd1.extend(["--wandb_api_key", wandb_key])
        print("Using wandb logging")
    else:
        print("No wandb API key found, running without wandb")
    
    print(f"Running command: {' '.join(cmd1)}")
    
    try:
        result = subprocess.run(cmd1, check=True, capture_output=True, text=True)
        print("✅ Base model evaluation completed successfully!")
        print("Output:", result.stdout[-500:])  # Last 500 chars
    except subprocess.CalledProcessError as e:
        print("❌ Base model evaluation failed!")
        print("Error:", e.stderr)
        return False
    
    # Example 2: Evaluate a fine-tuned model (if available)
    print("\n" + "="*60)
    print("EXAMPLE 2: Fine-tuned Model Evaluation")
    print("="*60)
    
    # Check if we have any fine-tuned models
    finetuned_dir = Path("./finetuned_models")
    if finetuned_dir.exists():
        model_dirs = [d for d in finetuned_dir.iterdir() if d.is_dir()]
        if model_dirs:
            model_path = str(model_dirs[0])  # Use first available model
            print(f"Found fine-tuned model: {model_path}")
            
            cmd2 = [
                sys.executable, "evaluate_dialogsum_benchmark.py",
                "--model_id", model_path,
                "--dataset_id", "knkarthick/dialogsum",
                "--split_name", "test",
                "--max_samples", "50",
                "--batch_size", "2",
                "--seed", "42",
                "--output_path", f"./evaluation_results/{model_dirs[0].name}",
                "--save_predictions"
            ]
            
            if wandb_key:
                cmd2.extend(["--wandb_api_key", wandb_key])
            
            print(f"Running command: {' '.join(cmd2)}")
            
            try:
                result = subprocess.run(cmd2, check=True, capture_output=True, text=True)
                print("✅ Fine-tuned model evaluation completed successfully!")
                print("Output:", result.stdout[-500:])
            except subprocess.CalledProcessError as e:
                print("❌ Fine-tuned model evaluation failed!")
                print("Error:", e.stderr)
        else:
            print("No fine-tuned models found in ./finetuned_models")
    else:
        print("No finetuned_models directory found")
    
    print("\n" + "="*60)
    print("EVALUATION EXAMPLES COMPLETED")
    print("="*60)
    print("Check the ./evaluation_results directory for outputs")
    
    return True

def show_usage_examples():
    """Show different usage examples."""
    print("="*60)
    print("USAGE EXAMPLES")
    print("="*60)
    
    examples = [
        {
            "name": "Basic DialogSum Evaluation",
            "cmd": [
                "python evaluate_dialogsum_benchmark.py",
                "--model_id meta-llama/Llama-2-7b-hf",
                "--dataset_id knkarthick/dialogsum",
                "--max_samples 100",
                "--wandb_api_key YOUR_API_KEY"
            ]
        },
        {
            "name": "Fine-tuned Model Evaluation",
            "cmd": [
                "python evaluate_dialogsum_benchmark.py",
                "--model_id ./finetuned_models/moderate_10samples_instruction",
                "--dataset_id knkarthick/dialogsum",
                "--max_samples 100",
                "--save_predictions",
                "--wandb_api_key YOUR_API_KEY"
            ]
        },
        {
            "name": "Custom Dataset Evaluation",
            "cmd": [
                "python evaluate_dialogsum_benchmark.py",
                "--model_id meta-llama/Llama-2-7b-hf",
                "--dataset_id /path/to/custom/dataset",
                "--from_disk",
                "--split_name test",
                "--context_length 512",
                "--batch_size 8"
            ]
        },
        {
            "name": "Seq2Seq Model Evaluation",
            "cmd": [
                "python evaluate_dialogsum_benchmark.py",
                "--model_id google/flan-t5-base",
                "--dataset_id knkarthick/dialogsum",
                "--seq2seq",
                "--max_samples 50"
            ]
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['name']}:")
        print("   " + " \\\n   ".join(example['cmd']))
    
    print("\n" + "="*60)
    print("WANDB CONFIGURATION")
    print("="*60)
    print("To fix wandb logging issues:")
    print("1. Set your API key: export WANDB_API_KEY=your_key_here")
    print("2. Or pass it as argument: --wandb_api_key your_key_here")
    print("3. The script uses project name 'dialogsum-benchmark'")
    print("4. Make sure you have access to this project or it will be created")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--run-example":
        run_evaluation_example()
    else:
        show_usage_examples()
        print(f"\nTo run actual examples: python {sys.argv[0]} --run-example")
