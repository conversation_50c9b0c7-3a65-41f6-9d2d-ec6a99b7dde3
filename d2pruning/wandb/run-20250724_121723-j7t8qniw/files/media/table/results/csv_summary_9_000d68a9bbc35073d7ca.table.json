{"columns": ["method_key", "selection_method", "num_samples", "rouge1", "rouge2", "rougeL", "training_time", "generation_time", "total_time"], "data": [["random_10samples_instruction", "random", 10, 0.13429257959048574, 0.03040100460224757, 0.11848402506372993, 25.754123210906982, 1760.6985383033752, 1786.4526615142822], ["k_center_10samples_instruction", "k_center", 10, 0.1273270653744244, 0.014008011164724856, 0.11082953583341493, 21.47499704360962, 1716.9473922252655, 1738.4223892688751], ["moderate_10samples_instruction", "moderate", 10, 0.115331773079663, 0.01686414176761905, 0.09755064531697913, 22.551298141479492, 1693.7780017852783, 1716.3292999267578]]}