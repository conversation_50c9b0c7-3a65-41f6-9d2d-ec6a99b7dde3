07/24/2025 22:30:37 - INFO - wandb_logger - Initialized W&B run: selection_random_10
07/24/2025 22:30:37 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/iijfzhdl
07/24/2025 22:30:37 - INFO - __main__ - Starting DialogSum data selection pipeline
07/24/2025 22:30:37 - INFO - __main__ - Selection method: random
07/24/2025 22:30:37 - INFO - __main__ - Number of samples: 10
07/24/2025 22:30:37 - INFO - __main__ - Embedding model: meta-llama/Llama-2-7b-hf
07/24/2025 22:30:37 - INFO - __main__ - Loading DialogSum dataset...
07/24/2025 22:30:37 - INFO - __main__ - Loading cached DialogSum train dataset from cache/dialogsum_train.pkl
07/24/2025 22:30:37 - INFO - __main__ - Loading cached DialogSum test dataset from cache/dialogsum_test.pkl
07/24/2025 22:30:37 - INFO - __main__ - Train dataset size: 12460
07/24/2025 22:30:37 - INFO - __main__ - Test dataset size: 1500
07/24/2025 22:30:38 - INFO - __main__ - Preprocessed 12460 training samples
07/24/2025 22:30:38 - INFO - __main__ - Average dialogue length: 131.0 words
07/24/2025 22:30:38 - INFO - __main__ - Average summary length: 22.9 words
07/24/2025 22:30:38 - INFO - wandb_logger - Logged dataset information to W&B
07/24/2025 22:30:38 - INFO - __main__ - Extracting embeddings...
07/24/2025 22:30:38 - INFO - __main__ - Loading LLaMA model for embeddings: meta-llama/Llama-2-7b-hf
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]
