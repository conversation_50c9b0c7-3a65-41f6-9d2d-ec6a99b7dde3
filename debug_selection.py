#!/usr/bin/env python3
"""
Debug version of the dialog selection script to identify issues.
"""

import os
import sys
import time
import logging
import traceback

# Set up more verbose logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("debug_script")

def run_with_debug():
    """Run the selection script with additional debugging."""
    try:
        logger.info("Starting debug session")
        
        # Print environment information
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Working directory: {os.getcwd()}")
        logger.info(f"PYTHONPATH: {os.environ.get('PYTHONPATH')}")
        
        # Get command line arguments
        method = sys.argv[1] if len(sys.argv) > 1 else "random"
        num_samples = int(sys.argv[2]) if len(sys.argv) > 2 else 50
        
        logger.info(f"Selection method: {method}")
        logger.info(f"Number of samples: {num_samples}")
        
        # Check cache directory
        cache_dir = os.path.join("d2pruning", "cache")
        logger.info(f"Checking cache directory: {cache_dir}")
        if os.path.exists(cache_dir):
            cache_files = os.listdir(cache_dir)
            logger.info(f"Found {len(cache_files)} files in cache directory")
            for file in cache_files:
                logger.info(f"  - {file}")
        else:
            logger.warning(f"Cache directory {cache_dir} does not exist!")
        
        # Try to load the DialogSum dataset directly
        logger.info("Attempting to load DialogSum dataset...")
        try:
            import pickle
            from datasets import load_dataset
            from pathlib import Path
            
            cache_file = Path(cache_dir) / "dialogsum_train.pkl"
            if cache_file.exists():
                logger.info(f"Loading cached dataset from {cache_file}")
                with open(cache_file, 'rb') as f:
                    try:
                        dataset = pickle.load(f)
                        logger.info(f"Successfully loaded dataset with {len(dataset)} samples")
                    except Exception as e:
                        logger.error(f"Error loading pickled dataset: {str(e)}")
                        logger.info("Trying to load dataset from HuggingFace instead")
                        dataset = load_dataset("knkarthick/dialogsum", split="train")
                        logger.info(f"Successfully loaded dataset from HuggingFace with {len(dataset)} samples")
            else:
                logger.info("No cached dataset found. Loading from HuggingFace")
                dataset = load_dataset("knkarthick/dialogsum", split="train")
                logger.info(f"Successfully loaded dataset from HuggingFace with {len(dataset)} samples")
                
            # Check dataset structure
            logger.info(f"Dataset columns: {dataset.column_names}")
            logger.info(f"First sample: {dataset[0]}")
            
            # Try processing a small subset
            logger.info("Processing small subset for testing")
            subset = dataset.select(range(10))
            dialogues = [item['dialogue'] for item in subset]
            summaries = [item['summary'] for item in subset]
            logger.info(f"Successfully extracted 10 dialogues and summaries")
            
            # Now try to run the selection algorithm directly
            logger.info(f"Running selection algorithm: {method}")
            if method == "random":
                logger.info("Using direct random selection")
                import torch
                import numpy as np
                
                try:
                    from d2pruning.core.data import CoresetSelection
                    logger.info("Successfully imported CoresetSelection")
                    
                    total_samples = len(dataset)
                    logger.info(f"Total samples: {total_samples}")
                    logger.info(f"Selecting {num_samples} samples")
                    
                    # Set seed for reproducibility
                    torch.manual_seed(42)
                    np.random.seed(42)
                    
                    selected_indices = CoresetSelection.random_selection(
                        total_num=total_samples,
                        num=num_samples
                    ).numpy()
                    
                    logger.info(f"Selection successful! Selected indices: {selected_indices[:10]}...")
                    
                    # Try to create a results file
                    output_dir = os.path.join("d2pruning", "results")
                    os.makedirs(output_dir, exist_ok=True)
                    
                    results_file = os.path.join(output_dir, f"selection_results_{method}_{num_samples}.json")
                    logger.info(f"Saving results to {results_file}")
                    
                    import json
                    selected_dialogues = [dataset[i]['dialogue'] for i in selected_indices]
                    selected_summaries = [dataset[i]['summary'] for i in selected_indices]
                    
                    results = {
                        'selection_method': method,
                        'num_samples': num_samples,
                        'selected_indices': selected_indices.tolist(),
                        'selection_time': time.time(),
                        'train_dataset_size': len(dataset),
                        'selected_dialogues': selected_dialogues,
                        'selected_summaries': selected_summaries,
                        'metadata': {
                            'total_samples': len(dataset),
                            'avg_dialogue_length': sum(len(d.split()) for d in dialogues[:100]) / 100,
                            'avg_summary_length': sum(len(s.split()) for s in summaries[:100]) / 100
                        }
                    }
                    
                    with open(results_file, 'w') as f:
                        json.dump(results, f, indent=2)
                    
                    logger.info(f"Results saved successfully to {results_file}")
                    logger.info("DEBUG RUN COMPLETED SUCCESSFULLY")
                    
                except ImportError as e:
                    logger.error(f"ImportError: {str(e)}")
                    logger.error("Make sure the d2pruning package is correctly installed")
                    logger.error(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
                    logger.error(f"Current directory: {os.getcwd()}")
                    logger.error(f"Directory contents: {os.listdir('.')}")
                    if os.path.exists('d2pruning'):
                        logger.error(f"d2pruning contents: {os.listdir('d2pruning')}")
                    if os.path.exists('d2pruning/core'):
                        logger.error(f"d2pruning/core contents: {os.listdir('d2pruning/core')}")
                
            else:
                logger.warning(f"Debug script only supports 'random' selection method for now")
        
        except Exception as e:
            logger.error(f"Error loading dataset: {str(e)}")
            logger.error(traceback.format_exc())
    
    except Exception as e:
        logger.error(f"Unexpected error in debug script: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    run_with_debug()
