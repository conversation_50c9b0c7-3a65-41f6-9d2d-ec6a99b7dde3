07/25/2025 16:30:49 - INFO - __main__ - Loading DialogSum test dataset...
07/25/2025 16:30:53 - INFO - __main__ - Loaded 500 test samples.
07/25/2025 16:30:53 - INFO - __main__ - 
--- Evaluating model: d2pruning_10samples_chat ---
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: WARNING Using a boolean value for 'reinit' is deprecated. Use 'return_previous' or 'finish_previous' instead.
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_163054-n74c6f9q
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run eval_d2pruning_10samples_chat
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/data-selection-experiments
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/data-selection-experiments/runs/n74c6f9q
07/25/2025 16:30:56 - INFO - __main__ - Loading model from d2pruning/finetuned_models/d2pruning_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 16:30:57 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:19<00:19, 19.74s/it]