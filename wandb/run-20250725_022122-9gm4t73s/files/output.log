07/25/2025 02:21:23 - INFO - __main__ - Loading model from d2pruning/finetuned_models/random_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 02:21:24 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████| 2/2 [00:24<00:00, 12.03s/it]
07/25/2025 02:21:51 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 02:21:51 - INFO - __main__ - Starting summary generation...
Generating Summaries:   0%|                                                                  | 0/2 [00:00<?, ?it/s]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
Generating Summaries: 100%|██████████████████████████████████████████████████████████| 2/2 [00:58<00:00, 29.36s/it]
07/25/2025 02:22:49 - INFO - __main__ - Generated 5 summaries.
07/25/2025 02:22:49 - INFO - __main__ - Computing and logging metrics...
Calculating ROUGE:   0%|                                                                     | 0/1 [00:00<?, ?it/s]07/25/2025 02:22:50 - INFO - absl - Using default tokenizer.
07/25/2025 02:22:50 - INFO - absl - Using default tokenizer.
Calculating ROUGE:   0%|                                                                     | 0/1 [00:00<?, ?it/s]
07/25/2025 02:22:50 - ERROR - __main__ - Failed to evaluate model random_10samples_chat: 'numpy.float64' object has no attribute 'mid'
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 276, in main
    rouge_scores = evaluator.compute_and_log_metrics(predictions, references)
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 184, in compute_and_log_metrics
    "eval/rouge1": agg_scores["rouge1"].mid.fmeasure,
AttributeError: 'numpy.float64' object has no attribute 'mid'. Did you mean: 'min'?
