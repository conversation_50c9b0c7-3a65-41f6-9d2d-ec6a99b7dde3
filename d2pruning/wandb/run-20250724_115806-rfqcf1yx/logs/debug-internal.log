{"time":"2025-07-24T11:58:06.85882609+07:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-24T11:58:07.349286715+07:00","level":"INFO","msg":"stream: created new stream","id":"rfqcf1yx"}
{"time":"2025-07-24T11:58:07.349375679+07:00","level":"INFO","msg":"stream: started","id":"rfqcf1yx"}
{"time":"2025-07-24T11:58:07.349539439+07:00","level":"INFO","msg":"handler: started","stream_id":"rfqcf1yx"}
{"time":"2025-07-24T11:58:07.349596437+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"rfqcf1yx"}
{"time":"2025-07-24T11:58:07.350122061+07:00","level":"INFO","msg":"sender: started","stream_id":"rfqcf1yx"}
{"time":"2025-07-24T12:00:07.737200522+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:00:22.737209336+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:00:37.737272347+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:00:52.738552936+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:07.737728829+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:22.737416486+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:37.737692887+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:52.737446889+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:02:07.738436737+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:02:22.737308809+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:02:25.797439359+07:00","level":"INFO","msg":"stream: closing","id":"rfqcf1yx"}
{"time":"2025-07-24T12:02:26.836810524+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-24T12:02:27.205955962+07:00","level":"INFO","msg":"handler: closed","stream_id":"rfqcf1yx"}
{"time":"2025-07-24T12:02:27.206054524+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"rfqcf1yx"}
{"time":"2025-07-24T12:02:27.206053902+07:00","level":"INFO","msg":"sender: closed","stream_id":"rfqcf1yx"}
{"time":"2025-07-24T12:02:27.206193817+07:00","level":"INFO","msg":"stream: closed","id":"rfqcf1yx"}
