{"os": "Linux-6.8.0-60-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.12", "startedAt": "2025-07-25T01:01:32.120540Z", "args": ["--model_dirs", "d2pruning/finetuned_models/diversity_10samples_chat", "d2pruning/finetuned_models/k_center_10samples_chat", "d2pruning/finetuned_models/moderate_10samples_chat", "d2pruning/finetuned_models/random_10samples_chat", "--base_model", "meta-llama/Llama-2-7b-hf", "--max_test_samples", "50", "--enable_wandb"], "program": "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", "codePath": "d2pruning/evaluate_summaries.py", "codePathLocal": "d2pruning/evaluate_summaries.py", "email": "<EMAIL>", "root": "/storage/nammt/data_selection_for_assistant_model", "host": "csews-Precision-7920-Tower", "executable": "/storage/nammt/data_selection_for_assistant_model/venv/bin/python", "cpu_count": 6, "cpu_count_logical": 6, "gpu": "NVIDIA RTX A4000", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "33291022336"}, "gpu_nvidia": [{"name": "NVIDIA RTX A4000", "memoryTotal": "17171480576", "cudaCores": 6144, "architecture": "Ampere", "uuid": "GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a"}], "cudaVersion": "12.9", "writerId": "vtxgzatfya5e6p3w948hc9928kbtx95o"}