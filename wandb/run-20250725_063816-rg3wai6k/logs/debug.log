2025-07-25 06:38:16,018 INFO    MainThread:3280998 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-25 06:38:16,018 INFO    MainThread:3280998 [wandb_setup.py:_flush():80] Configure stats pid to 3280998
2025-07-25 06:38:16,018 INFO    MainThread:3280998 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-25 06:38:16,018 INFO    MainThread:3280998 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/wandb/settings
2025-07-25 06:38:16,019 INFO    MainThread:3280998 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-25 06:38:16,019 INFO    MainThread:3280998 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_063816-rg3wai6k/logs/debug.log
2025-07-25 06:38:16,019 INFO    MainThread:3280998 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_063816-rg3wai6k/logs/debug-internal.log
2025-07-25 06:38:16,019 INFO    MainThread:3280998 [wandb_init.py:init():830] calling init triggers
2025-07-25 06:38:16,019 INFO    MainThread:3280998 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_results': 'd2pruning/results/selection_results_moderate_10.json', 'model_name': 'meta-llama/Llama-2-7b-hf', 'format_type': 'chat', 'num_epochs': 3, 'learning_rate': 0.0002, 'enable_wandb': True, 'wandb_project': 'data-selection-experiments', 'wandb_run_group': None, 'wandb_run_name': 'finetune_moderate_10', 'seed': 42, 'cache_dir': 'd2pruning/cache', 'output_dir': 'd2pruning/finetuned_models', 'selection_method': 'moderate', 'num_samples': 10, 'phase': 'fine-tuning', '_wandb': {}}
2025-07-25 06:38:16,019 INFO    MainThread:3280998 [wandb_init.py:init():871] starting backend
2025-07-25 06:38:16,264 INFO    MainThread:3280998 [wandb_init.py:init():874] sending inform_init request
2025-07-25 06:38:16,273 INFO    MainThread:3280998 [wandb_init.py:init():882] backend started and connected
2025-07-25 06:38:16,278 INFO    MainThread:3280998 [wandb_init.py:init():953] updated telemetry
2025-07-25 06:38:16,280 INFO    MainThread:3280998 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-25 06:38:17,141 INFO    MainThread:3280998 [wandb_init.py:init():1029] starting run threads in backend
2025-07-25 06:38:18,552 INFO    MainThread:3280998 [wandb_run.py:_console_start():2458] atexit reg
2025-07-25 06:38:18,552 INFO    MainThread:3280998 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-25 06:38:18,553 INFO    MainThread:3280998 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-25 06:38:18,553 INFO    MainThread:3280998 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-25 06:38:18,571 INFO    MainThread:3280998 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-25 06:39:32,991 INFO    MainThread:3280998 [wandb_run.py:_config_callback():1363] config_cb None None {'peft_config': {'default': {'task_type': <TaskType.CAUSAL_LM: 'CAUSAL_LM'>, 'peft_type': <PeftType.LORA: 'LORA'>, 'auto_mapping': None, 'base_model_name_or_path': 'meta-llama/Llama-2-7b-hf', 'revision': None, 'inference_mode': False, 'r': 16, 'target_modules': {'o_proj', 'k_proj', 'v_proj', 'q_proj'}, 'exclude_modules': None, 'lora_alpha': 32, 'lora_dropout': 0.1, 'fan_in_fan_out': False, 'bias': 'none', 'use_rslora': False, 'modules_to_save': None, 'init_lora_weights': True, 'layers_to_transform': None, 'layers_pattern': None, 'rank_pattern': {}, 'alpha_pattern': {}, 'megatron_config': None, 'megatron_core': 'megatron.core', 'trainable_token_indices': None, 'loftq_config': {}, 'eva_config': None, 'corda_config': None, 'use_dora': False, 'use_qalora': False, 'qalora_group_size': 16, 'layer_replication': None, 'runtime_config': {'ephemeral_gpu_offload': False}, 'lora_bias': False}}, 'vocab_size': 32000, 'max_position_embeddings': 4096, 'hidden_size': 4096, 'intermediate_size': 11008, 'num_hidden_layers': 32, 'num_attention_heads': 32, 'num_key_value_heads': 32, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-05, 'pretraining_tp': 1, 'use_cache': True, 'rope_theta': 10000.0, 'rope_scaling': None, 'attention_bias': False, 'attention_dropout': 0.0, 'mlp_bias': False, 'head_dim': 128, 'return_dict': True, 'output_hidden_states': False, 'torchscript': False, 'torch_dtype': 'float16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': False, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['LlamaForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 1, 'pad_token_id': None, 'eos_token_id': 2, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': 'meta-llama/Llama-2-7b-hf', 'transformers_version': '4.53.2', 'model_type': 'llama', 'quantization_config': {'quant_method': 'BITS_AND_BYTES', '_load_in_8bit': False, '_load_in_4bit': True, 'llm_int8_threshold': 6.0, 'llm_int8_skip_modules': None, 'llm_int8_enable_fp32_cpu_offload': False, 'llm_int8_has_fp16_weight': False, 'bnb_4bit_quant_type': 'nf4', 'bnb_4bit_use_double_quant': True, 'bnb_4bit_compute_dtype': 'float16', 'bnb_4bit_quant_storage': 'uint8', 'load_in_4bit': True, 'load_in_8bit': False}, 'output_attentions': False, 'output_dir': 'd2pruning/finetuned_models/moderate_10samples_chat', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': False, 'do_predict': False, 'eval_strategy': 'no', 'prediction_loss_only': False, 'per_device_train_batch_size': 1, 'per_device_eval_batch_size': 8, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 4, 'eval_accumulation_steps': None, 'eval_delay': 0, 'torch_empty_cache_steps': None, 'learning_rate': 0.0002, 'weight_decay': 0.0, 'adam_beta1': 0.9, 'adam_beta2': 0.999, 'adam_epsilon': 1e-08, 'max_grad_norm': 1.0, 'num_train_epochs': 3, 'max_steps': -1, 'lr_scheduler_type': 'linear', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.0, 'warmup_steps': 100, 'log_level': 'info', 'log_level_replica': 'warning', 'log_on_each_node': True, 'logging_dir': 'd2pruning/finetuned_models/moderate_10samples_chat/runs/Jul25_06-39-32_csews-Precision-7920-Tower', 'logging_strategy': 'steps', 'logging_first_step': True, 'logging_steps': 1, 'logging_nan_inf_filter': False, 'save_strategy': 'epoch', 'save_steps': 500, 'save_total_limit': None, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': False, 'restore_callback_states_from_checkpoint': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': False, 'fp16': True, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': None, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': False, 'eval_steps': None, 'dataloader_num_workers': 0, 'dataloader_prefetch_factor': None, 'past_index': -1, 'run_name': 'moderate_10samples', 'disable_tqdm': False, 'remove_unused_columns': False, 'label_names': None, 'load_best_model_at_end': False, 'metric_for_best_model': None, 'greater_is_better': None, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'accelerator_config': {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}, 'deepspeed': None, 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': None, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': False, 'dataloader_persistent_workers': False, 'skip_memory_metrics': True, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': None, 'hub_always_push': False, 'hub_revision': None, 'gradient_checkpointing': False, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': False, 'include_for_metrics': [], 'eval_do_concat_batches': True, 'fp16_backend': 'auto', 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'optim_target_modules': None, 'batch_eval_metrics': False, 'eval_on_start': False, 'use_liger_kernel': False, 'liger_kernel_config': None, 'eval_use_gather_object': False, 'average_tokens_across_devices': False}
2025-07-25 06:39:33,000 INFO    MainThread:3280998 [wandb_config.py:__setitem__():154] [no run ID] config set model/num_parameters = 6755192832 - <bound method Run._config_callback of <wandb.sdk.wandb_run.Run object at 0x7cc2f83a7cd0>>
2025-07-25 06:39:33,000 INFO    MainThread:3280998 [wandb_run.py:_config_callback():1363] config_cb model/num_parameters 6755192832 None
