07/24/2025 19:57:43 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_chat_20250724_195741
07/24/2025 19:57:43 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/tdhvggic
07/24/2025 19:57:43 - INFO - __main__ - ============================================================
07/24/2025 19:57:43 - INFO - __main__ - STEP 1: DATA SELECTION
07/24/2025 19:57:43 - INFO - __main__ - ============================================================
07/24/2025 19:57:43 - INFO - __main__ - Running data selection with method: random
07/24/2025 19:57:43 - INFO - __main__ - Selection results already exist: results/selection_results_random_10.json
07/24/2025 19:57:43 - INFO - __main__ - Running data selection with method: moderate
07/24/2025 19:57:43 - INFO - __main__ - Selection results already exist: results/selection_results_moderate_10.json
07/24/2025 19:57:43 - INFO - __main__ - Running data selection with method: k_center
07/24/2025 19:57:43 - INFO - __main__ - Selection results already exist: results/selection_results_k_center_10.json
07/24/2025 19:57:43 - INFO - __main__ - Running data selection with method: diversity
07/24/2025 19:57:43 - INFO - __main__ - Selection results already exist: results/selection_results_diversity_10.json
07/24/2025 19:57:43 - INFO - __main__ - ============================================================
07/24/2025 19:57:43 - INFO - __main__ - STEP 2: FINE-TUNING
07/24/2025 19:57:43 - INFO - __main__ - ============================================================
07/24/2025 19:57:43 - INFO - __main__ - Running fine-tuning for results/selection_results_random_10.json
07/24/2025 19:57:43 - INFO - __main__ - Fine-tuned model already exists: finetuned_models/random_10samples_chat
07/24/2025 19:57:43 - INFO - __main__ - Running fine-tuning for results/selection_results_moderate_10.json
07/24/2025 19:57:43 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_moderate_10.json --model_name meta-llama/Llama-2-7b-hf --format_type chat --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_moderate_10
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 404, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 386, in main
    experiment_summary = experiment.run_complete_experiment(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 226, in run_complete_experiment
    model_dir = self.run_finetuning(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 124, in run_finetuning
    result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
  File "/usr/lib/python3.10/subprocess.py", line 505, in run
    stdout, stderr = process.communicate(input, timeout=timeout)
  File "/usr/lib/python3.10/subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "/usr/lib/python3.10/subprocess.py", line 2021, in _communicate
    ready = selector.select(timeout)
  File "/usr/lib/python3.10/selectors.py", line 416, in select
    fd_event_list = self._selector.poll(timeout)
KeyboardInterrupt
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 404, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 386, in main
    experiment_summary = experiment.run_complete_experiment(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 226, in run_complete_experiment
    model_dir = self.run_finetuning(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 124, in run_finetuning
    result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
  File "/usr/lib/python3.10/subprocess.py", line 505, in run
    stdout, stderr = process.communicate(input, timeout=timeout)
  File "/usr/lib/python3.10/subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "/usr/lib/python3.10/subprocess.py", line 2021, in _communicate
    ready = selector.select(timeout)
  File "/usr/lib/python3.10/selectors.py", line 416, in select
    fd_event_list = self._selector.poll(timeout)
KeyboardInterrupt
