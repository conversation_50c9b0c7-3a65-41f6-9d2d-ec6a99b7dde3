{"time":"2025-07-24T08:46:53.464337917+07:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-24T08:46:53.80798179+07:00","level":"INFO","msg":"stream: created new stream","id":"twisk0w1"}
{"time":"2025-07-24T08:46:53.808081146+07:00","level":"INFO","msg":"stream: started","id":"twisk0w1"}
{"time":"2025-07-24T08:46:53.808176933+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"twisk0w1"}
{"time":"2025-07-24T08:46:53.808189502+07:00","level":"INFO","msg":"sender: started","stream_id":"twisk0w1"}
{"time":"2025-07-24T08:46:53.808247143+07:00","level":"INFO","msg":"handler: started","stream_id":"twisk0w1"}
{"time":"2025-07-24T08:47:09.181221181+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T08:47:24.181600442+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T08:47:26.534546777+07:00","level":"INFO","msg":"stream: closing","id":"twisk0w1"}
{"time":"2025-07-24T08:47:27.845633494+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-24T08:47:28.203730109+07:00","level":"INFO","msg":"handler: closed","stream_id":"twisk0w1"}
{"time":"2025-07-24T08:47:28.203772169+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"twisk0w1"}
{"time":"2025-07-24T08:47:28.203793889+07:00","level":"INFO","msg":"sender: closed","stream_id":"twisk0w1"}
{"time":"2025-07-24T08:47:28.203903215+07:00","level":"INFO","msg":"stream: closed","id":"twisk0w1"}
