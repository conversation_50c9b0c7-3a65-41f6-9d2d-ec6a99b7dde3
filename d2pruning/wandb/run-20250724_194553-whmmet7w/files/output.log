07/24/2025 19:45:54 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 19:45:54 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/whmmet7w
07/24/2025 19:45:54 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 19:45:57 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards:   0%|          | 0/2 [00:15<?, ?it/s]
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 505, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 466, in main
    finetuner.load_model_and_tokenizer()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 213, in load_model_and_tokenizer
    self.model = AutoModelForCausalLM.from_pretrained(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/models/auto/auto_factory.py", line 600, in from_pretrained
    return model_class.from_pretrained(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 311, in _wrapper
    return func(*args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 4839, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 5302, in _load_pretrained_model
    _error_msgs, disk_offload_index, cpu_offload_index = load_shard_file(args)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 933, in load_shard_file
    disk_offload_index, cpu_offload_index = _load_state_dict_into_meta_model(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
    return func(*args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 848, in _load_state_dict_into_meta_model
    hf_quantizer.create_quantized_param(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/quantizers/quantizer_bnb_4bit.py", line 242, in create_quantized_param
    new_value = param_value.to("cpu")
KeyboardInterrupt
