2025-07-25 09:54:45,728 INFO    MainThread:3510116 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-25 09:54:45,728 INFO    MainThread:3510116 [wandb_setup.py:_flush():80] Configure stats pid to 3510116
2025-07-25 09:54:45,728 INFO    MainThread:3510116 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-25 09:54:45,728 INFO    MainThread:3510116 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-25 09:54:45,729 INFO    MainThread:3510116 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-25 09:54:45,729 INFO    MainThread:3510116 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250725_095445-4ptyud2u/logs/debug.log
2025-07-25 09:54:45,729 INFO    MainThread:3510116 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250725_095445-4ptyud2u/logs/debug-internal.log
2025-07-25 09:54:45,729 INFO    MainThread:3510116 [wandb_init.py:init():830] calling init triggers
2025-07-25 09:54:45,729 INFO    MainThread:3510116 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-07-25 09:54:45,729 INFO    MainThread:3510116 [wandb_init.py:init():871] starting backend
2025-07-25 09:54:47,183 INFO    MainThread:3510116 [wandb_init.py:init():874] sending inform_init request
2025-07-25 09:54:47,243 INFO    MainThread:3510116 [wandb_init.py:init():882] backend started and connected
2025-07-25 09:54:47,245 INFO    MainThread:3510116 [wandb_init.py:init():953] updated telemetry
2025-07-25 09:54:47,258 INFO    MainThread:3510116 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-25 09:54:48,437 INFO    MainThread:3510116 [wandb_init.py:init():1029] starting run threads in backend
