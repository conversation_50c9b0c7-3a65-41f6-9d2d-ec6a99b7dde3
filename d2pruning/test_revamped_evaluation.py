#!/usr/bin/env python3
"""
Test script for the revamped DialogSum evaluation pipeline.
This tests the new evaluation format with proper wandb integration.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_evaluation_script():
    """Test the revamped evaluation script."""
    logger.info("Testing revamped DialogSum evaluation...")
    
    # Check if we have any fine-tuned models
    finetuned_dir = Path("./finetuned_models")
    if not finetuned_dir.exists():
        logger.error("No finetuned_models directory found. Run the training pipeline first.")
        return False
    
    model_dirs = [d for d in finetuned_dir.iterdir() if d.is_dir()]
    if not model_dirs:
        logger.error("No fine-tuned models found. Run the training pipeline first.")
        return False
    
    # Use first available model for testing
    test_model = str(model_dirs[0])
    logger.info(f"Testing with model: {test_model}")
    
    # Test command
    cmd = [
        sys.executable, "evaluate_summaries.py",
        "--model_dirs", test_model,
        "--base_model", "meta-llama/Llama-2-7b-hf",
        "--max_test_samples", "10",  # Small sample for testing
        "--batch_size", "2",
        "--seed", "42",
        "--output_dir", "./test_evaluation_results",
        "--cache_dir", "./cache"
    ]
    
    # Add wandb if API key is available
    wandb_key = os.getenv("WANDB_API_KEY")
    if wandb_key:
        cmd.extend(["--enable_wandb", "--wandb_api_key", wandb_key])
        logger.info("Using wandb logging")
    else:
        logger.info("No wandb API key found, running without wandb")
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, cwd=".")
        logger.info("✅ Evaluation test completed successfully!")
        logger.info("Output (last 500 chars):")
        logger.info(result.stdout[-500:])
        return True
    except subprocess.CalledProcessError as e:
        logger.error("❌ Evaluation test failed!")
        logger.error(f"Return code: {e.returncode}")
        logger.error(f"Error output: {e.stderr}")
        logger.error(f"Standard output: {e.stdout}")
        return False

def test_full_pipeline():
    """Test the full DialogSum experiment pipeline."""
    logger.info("Testing full DialogSum experiment pipeline...")
    
    cmd = [
        sys.executable, "run_dialogsum_experiment.py",
        "--selection_methods", "random",  # Just test one method
        "--num_samples", "5",  # Very small for testing
        "--max_test_samples", "10",  # Small evaluation
        "--num_epochs", "1",  # Quick training
        "--base_dir", "./test_experiment"
    ]
    
    # Add wandb if available
    wandb_key = os.getenv("WANDB_API_KEY")
    if wandb_key:
        cmd.append("--enable_wandb")
        logger.info("Using wandb logging")
    else:
        cmd.append("--disable_wandb")
        logger.info("Running without wandb")
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, cwd=".")
        logger.info("✅ Full pipeline test completed successfully!")
        logger.info("Output (last 500 chars):")
        logger.info(result.stdout[-500:])
        return True
    except subprocess.CalledProcessError as e:
        logger.error("❌ Full pipeline test failed!")
        logger.error(f"Return code: {e.returncode}")
        logger.error(f"Error output: {e.stderr}")
        return False

def check_dependencies():
    """Check if required dependencies are available."""
    logger.info("Checking dependencies...")
    
    try:
        import torch
        logger.info(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        logger.error("❌ PyTorch not found")
        return False
    
    try:
        import transformers
        logger.info(f"✅ Transformers: {transformers.__version__}")
    except ImportError:
        logger.error("❌ Transformers not found")
        return False
    
    try:
        import datasets
        logger.info(f"✅ Datasets: {datasets.__version__}")
    except ImportError:
        logger.error("❌ Datasets not found")
        return False
    
    try:
        import peft
        logger.info(f"✅ PEFT: {peft.__version__}")
    except ImportError:
        logger.error("❌ PEFT not found")
        return False
    
    # Check for score module
    try:
        sys.path.append("/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation")
        import score
        logger.info("✅ Custom score module found")
    except ImportError:
        logger.warning("⚠️  Custom score module not found, will use evaluate library")
    
    return True

def main():
    """Run all tests."""
    logger.info("="*60)
    logger.info("TESTING REVAMPED DIALOGSUM EVALUATION")
    logger.info("="*60)
    
    # Check dependencies
    if not check_dependencies():
        logger.error("Dependency check failed. Please install required packages.")
        return False
    
    # Test evaluation script
    logger.info("\n" + "="*40)
    logger.info("TEST 1: Evaluation Script")
    logger.info("="*40)
    
    eval_success = test_evaluation_script()
    
    if not eval_success:
        logger.error("Evaluation script test failed. Check the logs above.")
        return False
    
    # Test full pipeline (optional, takes longer)
    run_full_test = input("\nRun full pipeline test? (y/N): ").lower().strip() == 'y'
    
    if run_full_test:
        logger.info("\n" + "="*40)
        logger.info("TEST 2: Full Pipeline")
        logger.info("="*40)
        
        pipeline_success = test_full_pipeline()
        
        if not pipeline_success:
            logger.error("Full pipeline test failed. Check the logs above.")
            return False
    
    logger.info("\n" + "="*60)
    logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
    logger.info("="*60)
    logger.info("The revamped evaluation pipeline is working correctly.")
    logger.info("You can now run the full experiment with:")
    logger.info("python run_dialogsum_experiment.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
