#!/bin/bash
# setup_and_run_experiment.sh
# This script sets up the environment and runs the DialogSum experiment
# with proper wandb integration
#
# Usage: 
#   ./setup_and_run_experiment.sh [num_samples]
#   ./setup_and_run_experiment.sh 50     # Run with 50 samples
#   ./setup_and_run_experiment.sh -h     # Show help

# Show help message if requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    echo "Usage: ./setup_and_run_experiment.sh [num_samples] [methods]"
    echo ""
    echo "Arguments:"
    echo "  num_samples   Number of samples to select (default: 50)"
    echo "  methods       Specific selection methods to run, comma-separated"
    echo "                Options: random,moderate,k_center,diversity,d2pruning"
    echo "                Default: all methods"
    echo ""
    echo "Examples:"
    echo "  ./setup_and_run_experiment.sh 50"
    echo "  ./setup_and_run_experiment.sh 20 random,moderate"
    echo ""
    exit 0
fi



# Exit on error
set -e

# Check if WANDB_API_KEY is set
if [ -z "$WANDB_API_KEY" ]; then
    echo "Warning: WANDB_API_KEY is not set. Set it with: export WANDB_API_KEY=your_api_key"
    echo "You can get your API key from https://wandb.ai/settings"
    echo "Continuing without wandb logging..."
    
    # For testing, you can uncomment the following line and add your key
    # export WANDB_API_KEY="your-api-key-here"
    
    # Alternative: Generate a prompt for the user to enter their key
    read -p "Would you like to enter a Weights & Biases API key now? [y/N] " response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        read -p "Enter your W&B API key: " api_key
        export WANDB_API_KEY="$api_key"
        echo "WANDB_API_KEY set for this session."
    fi
fi

# Create necessary directories
mkdir -p selected_samples
mkdir -p experiment_results/visualizations
mkdir -p logs
mkdir -p d2pruning/results

# Create explicit visualization directories
echo "Creating visualization directories..."
mkdir -p d2pruning/results/viz
mkdir -p experiment_results/visualizations

# Make sure the utils_save_selection.py is executable
chmod +x d2pruning/utils_save_selection.py

# Check for required packages
echo "Checking for required Python packages..."
pip install -q matplotlib scikit-learn sentence-transformers datasets transformers torch numpy pandas

# Run the experiment with each selection method
# Methods and samples will be set based on command line arguments
NUM_SAMPLES=${1:-50}

# Default methods or use comma-separated list from second argument
DEFAULT_METHODS=("random" "moderate" "k_center" "diversity" "d2pruning")
if [[ -n "$2" ]]; then
    IFS=',' read -r -a METHODS <<< "$2"
else
    METHODS=("${DEFAULT_METHODS[@]}")
fi

echo "================================================================"
echo "Starting experiment with $NUM_SAMPLES samples per selection method"
echo "================================================================"

for method in "${METHODS[@]}"; do
    echo ""
    echo "================================================================"
    echo "Running selection with method: $method ($NUM_SAMPLES samples)"
    echo "================================================================"
    
    # Create result directory if it doesn't exist
    mkdir -p d2pruning/results
    
    # Remove any previous results for this method and sample size to ensure fresh run
    SELECTION_RESULTS_FILE="d2pruning/results/selection_results_${method}_${NUM_SAMPLES}.json"
    if [ -f "$SELECTION_RESULTS_FILE" ]; then
        echo "Removing previous results file: $SELECTION_RESULTS_FILE"
        rm "$SELECTION_RESULTS_FILE"
    fi
    
    # Remove any previous visualizations for this method and sample size
    VIZ_FILE="d2pruning/results/selection_viz_${method}_${NUM_SAMPLES}_tsne.png"
    if [ -f "$VIZ_FILE" ]; then
        echo "Removing previous visualization: $VIZ_FILE"
        rm "$VIZ_FILE"
    fi
    
    VIZ_FILE="d2pruning/results/selection_viz_${method}_${NUM_SAMPLES}_pca.png"
    if [ -f "$VIZ_FILE" ]; then
        echo "Removing previous visualization: $VIZ_FILE"
        rm "$VIZ_FILE"
    fi
    
    # Clear log file
    > logs/selection_${method}_${NUM_SAMPLES}.log
    
    if [ "$method" == "d2pruning" ]; then
        # D2Pruning requires training dynamics scores
        echo "Special handling for d2pruning - using pre-computed scores"

        # Check for training dynamics in multiple possible locations
        DYNAMICS_PATH=""
        POSSIBLE_PATHS=(
            "experiment_results/selection_results/training_dynamics_scores.npy"
            "d2pruning/experiment_results/selection_results/training_dynamics_scores.npy"
            "selected_samples/importance_scores.npy"
            "cache/training_dynamics_scores.npy"
        )

        for path in "${POSSIBLE_PATHS[@]}"; do
            if [ -f "$path" ]; then
                DYNAMICS_PATH="$path"
                echo "✅ Found training dynamics at: $path"
                break
            fi
        done

        if [ -z "$DYNAMICS_PATH" ]; then
            echo "❌ Training dynamics scores not found in any expected location:"
            for path in "${POSSIBLE_PATHS[@]}"; do
                echo "  - $path"
            done
            echo ""
            echo "Generating training dynamics scores..."

            # Create output directory
            mkdir -p experiment_results/selection_results

            # Generate training dynamics
            python d2pruning/generate_training_dynamics.py \
                --output_dir experiment_results/selection_results \
                --cache_dir d2pruning/cache \
                --seed 42 2>&1 | tee logs/generate_dynamics.log

            # Check if generation was successful
            if [ -f "experiment_results/selection_results/training_dynamics_scores.npy" ]; then
                DYNAMICS_PATH="experiment_results/selection_results/training_dynamics_scores.npy"
                echo "✅ Training dynamics generated successfully!"
            else
                echo "❌ Failed to generate training dynamics. Skipping d2pruning method."
                continue
            fi
        fi

        echo "Running selection for $method with $NUM_SAMPLES samples..."
        python -u d2pruning/dialogsum_selection.py \
            --selection_method $method \
            --num_samples $NUM_SAMPLES \
            --embedding_model meta-llama/Llama-2-7b-hf \
            --difficulty_score_method training_dynamics \
            --difficulty_scores_path "$DYNAMICS_PATH" \
            --enable_wandb \
            --visualize \
            --viz_method tsne \
            --output_dir d2pruning/results 2>&1 | tee -a logs/selection_${method}_${NUM_SAMPLES}.log
    else
        # Normal handling for other methods
        echo "Running selection for $method with $NUM_SAMPLES samples..."
        
        python -u d2pruning/dialogsum_selection.py \
            --selection_method $method \
            --num_samples $NUM_SAMPLES \
            --embedding_model meta-llama/Llama-2-7b-hf \
            --enable_wandb \
            --visualize \
            --viz_method tsne \
            --output_dir d2pruning/results 2>&1 | tee -a logs/selection_${method}_${NUM_SAMPLES}.log
    fi
    
    # Check if the selection was successful
    if [ ! -f "d2pruning/results/selection_results_${method}_${NUM_SAMPLES}.json" ]; then
        echo "ERROR: Selection failed for method ${method}. Selection results file not found."
        echo "Examine the log file logs/selection_${method}_${NUM_SAMPLES}.log for details."
        
        # Show the last few lines of the log to help diagnose the issue
        echo "Last 20 lines of log:"
        tail -n 20 logs/selection_${method}_${NUM_SAMPLES}.log
        
        # Print a more helpful error message
        echo ""
        echo "This error suggests the selection process failed before creating output files."
        echo "Possible issues:"
        echo "1. Dataset loading error - check cache/dialogsum_train.pkl exists"
        echo "2. Memory issues - try with fewer samples"
        echo "3. Missing dependencies - check all required packages are installed"
        
        echo "Skipping fine-tuning for this method and continuing with next method..."
        continue
    fi
    
    echo "Selection successful! Results saved to: d2pruning/results/selection_results_${method}_${NUM_SAMPLES}.json"
        
    # Copy the selected samples to a standardized location
    mkdir -p selected_samples/${method}_${NUM_SAMPLES}
    cp d2pruning/results/selection_results_${method}_${NUM_SAMPLES}.json selected_samples/${method}_${NUM_SAMPLES}/selected_samples.json
    
    echo "Fine-tuning with selected samples..."
    python d2pruning/finetune_llama.py \
        --selection_results d2pruning/results/selection_results_${method}_${NUM_SAMPLES}.json \
        --model_name "meta-llama/Llama-2-7b-hf" \
        --format_type "chat" \
        --num_epochs 3 \
        --cache_dir "d2pruning/cache" \
        --output_dir "d2pruning/finetuned_models" \
        --enable_wandb \
        --wandb_run_name "finetune_${method}_${NUM_SAMPLES}" 2>&1 | tee logs/finetune_${method}_${NUM_SAMPLES}.log
    
    # Check if visualization was created and log its location
    echo "Looking for visualization files..."
    
    VIZ_FILE="d2pruning/results/selection_viz_${method}_${NUM_SAMPLES}_tsne.png"
    STD_VIZ_FILE="experiment_results/visualizations/selection_viz_${method}_${NUM_SAMPLES}_tsne.png"
    
    # Try to find visualizations with any method (tsne or pca)
    if [ ! -f "$VIZ_FILE" ]; then
        VIZ_FILE="d2pruning/results/selection_viz_${method}_${NUM_SAMPLES}_pca.png"
    fi
    
    if [ ! -f "$STD_VIZ_FILE" ]; then
        STD_VIZ_FILE="experiment_results/visualizations/selection_viz_${method}_${NUM_SAMPLES}_pca.png"
    fi
    
    # Check if visualization files exist
    if [ -f "$VIZ_FILE" ]; then
        echo "✅ Visualization created at: $VIZ_FILE"
        # Copy to a standard location for easier viewing
        cp "$VIZ_FILE" "./visualization_${method}_${NUM_SAMPLES}.png" 
    else
        echo "⚠️ No visualization found at expected location: d2pruning/results/"
        find d2pruning/results -name "*.png" -type f
    fi
    
    # Also check the standardized location
    if [ -f "$STD_VIZ_FILE" ]; then
        echo "✅ Visualization also available at: $STD_VIZ_FILE"
        # Copy to a standard location for easier viewing
        cp "$STD_VIZ_FILE" "./visualization_${method}_${NUM_SAMPLES}.png" 
    fi
    
    # If we couldn't find any visualizations, check what happened to matplotlib
    if [ ! -f "$VIZ_FILE" ] && [ ! -f "$STD_VIZ_FILE" ]; then
        echo "📊 Checking matplotlib configuration..."
        python -c "import matplotlib; print(matplotlib.matplotlib_fname()); print('Backend:', matplotlib.get_backend())"
        
        echo "🔍 Checking for any png files that might have been created:"
        find /storage/nammt/data_selection_for_assistant_model -name "*.png" -type f -mtime -1
    fi
done

# Check if we have any finetuned models before evaluation
if ls d2pruning/finetuned_models/*/ 1> /dev/null 2>&1; then
    # Run evaluation on all models
    echo "Running evaluation on all finetuned models..."
    python d2pruning/evaluate_summaries.py \
        --model_dirs d2pruning/finetuned_models/* \
        --base_model "meta-llama/Llama-2-7b-hf" \
        --max_test_samples 500 \
        --batch_size 4 \
        --num_workers 2 \
        --seed 42 \
        --enable_wandb \
        --cache_dir d2pruning/cache 2>&1 | tee logs/eval_all_models.log
else
    echo "No finetuned models found. Skipping evaluation."
fi

echo "Experiment complete!"
