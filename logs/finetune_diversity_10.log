07/25/2025 07:32:15 - INFO - __main__ - Logging to ../logs/finetune_20250725_073215.log
07/25/2025 07:32:15 - INFO - __main__ - Loading selection results from d2pruning/results/selection_results_diversity_10.json
wandb: Currently logged in as: joshua<PERSON>shu<PERSON> (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: WARNING Using a boolean value for 'reinit' is deprecated. Use 'return_previous' or 'finish_previous' instead.
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_073216-1vbgk50j
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_diversity_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/1vbgk50j
07/25/2025 07:32:19 - INFO - wandb_logger - Initialized W&B run: finetune_diversity_10
07/25/2025 07:32:19 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/1vbgk50j
07/25/2025 07:32:19 - INFO - __main__ - W&B logging initialized with run name: finetune_diversity_10
07/25/2025 07:32:19 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 07:32:21 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:52<00:52, 52.95s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:59<00:00, 25.63s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:59<00:00, 29.73s/it]
07/25/2025 07:33:23 - INFO - __main__ - Preparing training dataset...
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 404.90 examples/s]
07/25/2025 07:33:45 - INFO - __main__ - Starting fine-tuning...
07/25/2025 07:33:45 - INFO - wandb_logger - Logged training start for diversity to W&B
Using auto half precision backend
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
***** Running training *****
  Num examples = 10
  Num Epochs = 3
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 4
  Gradient Accumulation steps = 4
  Total optimization steps = 9
  Number of trainable parameters = 16,777,216
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/9 [00:00<?, ?it/s]
 11%|█         | 1/9 [00:03<00:24,  3.01s/it]
                                             

 11%|█         | 1/9 [00:03<00:24,  3.01s/it]
 22%|██▏       | 2/9 [00:05<00:17,  2.43s/it]
                                             

 22%|██▏       | 2/9 [00:05<00:17,  2.43s/it]
 33%|███▎      | 3/9 [00:06<00:11,  1.84s/it]
                                             

 33%|███▎      | 3/9 [00:06<00:11,  1.84s/it]Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-3
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-3/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-3/special_tokens_map.json

 44%|████▍     | 4/9 [00:11<00:16,  3.25s/it]
                                             

 44%|████▍     | 4/9 [00:11<00:16,  3.25s/it]
 56%|█████▌    | 5/9 [00:13<00:11,  2.84s/it]
                                             

 56%|█████▌    | 5/9 [00:13<00:11,  2.84s/it]
 67%|██████▋   | 6/9 [00:14<00:06,  2.25s/it]
                                             

 67%|██████▋   | 6/9 [00:14<00:06,  2.25s/it]Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-6
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-6/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-6/special_tokens_map.json

 78%|███████▊  | 7/9 [00:20<00:06,  3.29s/it]
                                             

 78%|███████▊  | 7/9 [00:20<00:06,  3.29s/it]
 89%|████████▉ | 8/9 [00:22<00:02,  2.92s/it]
                                             

 89%|████████▉ | 8/9 [00:22<00:02,  2.92s/it]
100%|██████████| 9/9 [00:23<00:00,  2.29s/it]
                                             

100%|██████████| 9/9 [00:23<00:00,  2.29s/it]Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-9
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-9/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-9/special_tokens_map.json


Training completed. Do not forget to share your model on huggingface.co/models =)



                                             

100%|██████████| 9/9 [00:26<00:00,  2.29s/it]
100%|██████████| 9/9 [00:26<00:00,  2.92s/it]
07/25/2025 07:34:13 - INFO - wandb_logger - Logged training completion for diversity to W&B
Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/special_tokens_map.json
07/25/2025 07:34:14 - INFO - __main__ - Fine-tuning completed in 26.66 seconds
wandb: uploading output.log; uploading config.yaml
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:               experiment/total_duration ▁
wandb:                             train/epoch ▁▂▃▄▅▅▆▇██
wandb:                       train/global_step ▁▂▂▃▃▃▃▄▄▅▅▆▆▆▆▇▇████████
wandb:                         train/grad_norm ▁▆▂▄▃▂▁▃█
wandb:                     train/learning_rate ▁▂▃▄▅▅▆▇█
wandb:                              train/loss ▁▅▁▂▂▃▁▁█
wandb:           training/diversity/batch_size ▁
wandb:                training/diversity/epoch ▁▁
wandb:           training/diversity/final_loss ▁
wandb:            training/diversity/grad_norm ▁▆▂▄▃▂▁▃█
wandb:        training/diversity/learning_rate █▁▁▁▁▁▁▁▁▂▁
wandb:                 training/diversity/loss ▁▁
wandb:          training/diversity/loss_smooth ▁
wandb:           training/diversity/num_epochs ▁▁
wandb:          training/diversity/num_samples ▁
wandb:              training/diversity/runtime ▁
wandb:   training/diversity/samples_per_second ▁
wandb:           training/diversity/start_time ▁
wandb:                 training/diversity/step ▁▁
wandb:       training/diversity/time_per_epoch ▁
wandb:     training/diversity/total_parameters ▁
wandb:          training/diversity/total_steps ▁
wandb:           training/diversity/total_time ▁
wandb: training/diversity/trainable_parameters ▁
wandb:      training/diversity/trainable_ratio ▁
wandb: 
wandb: Run summary:
wandb:               experiment/total_duration 119.03469
wandb:                              total_flos 483613813702656.0
wandb:                             train/epoch 3
wandb:                       train/global_step 9
wandb:                         train/grad_norm 0.55205
wandb:                     train/learning_rate 2e-05
wandb:                              train/loss 2.4665
wandb:                              train_loss 2.05715
wandb:                           train_runtime 26.3052
wandb:                train_samples_per_second 1.14
wandb:                  train_steps_per_second 0.342
wandb:           training/diversity/batch_size 1
wandb:                training/diversity/epoch 3
wandb:           training/diversity/final_loss 2.05715
wandb:          training/diversity/format_type instruction
wandb:            training/diversity/grad_norm 0.55205
wandb:        training/diversity/learning_rate 0
wandb:                 training/diversity/loss 2.05715
wandb:          training/diversity/loss_smooth 2.05715
wandb:           training/diversity/model_name meta-llama/Llama-2-7...
wandb:           training/diversity/num_epochs 3
wandb:          training/diversity/num_samples 10
wandb:              training/diversity/runtime 26.3052
wandb:   training/diversity/samples_per_second 1.14
wandb:           training/diversity/start_time 1753403625.67377
wandb:               training/diversity/status completed
wandb:                 training/diversity/step 9
wandb:       training/diversity/time_per_epoch 8.88782
wandb:     training/diversity/total_parameters 3517190144
wandb:          training/diversity/total_steps 9
wandb:           training/diversity/total_time 26.66347
wandb: training/diversity/trainable_parameters 16777216
wandb:      training/diversity/trainable_ratio 0.00477
wandb: 
wandb: 🚀 View run finetune_diversity_10 at: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/1vbgk50j
wandb: ⭐️ View project at: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark
wandb: Synced 5 W&B file(s), 1 media file(s), 2 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250725_073216-1vbgk50j/logs
07/25/2025 07:34:16 - INFO - wandb_logger - Finished W&B run
07/25/2025 07:34:16 - INFO - __main__ - Training results saved to d2pruning/finetuned_models/diversity_10samples_chat/training_results.json
07/25/2025 07:34:16 - INFO - __main__ - Fine-tuning completed successfully!
07/25/2025 07:34:16 - INFO - __main__ - Fine-tuning complete.
{'loss': 1.9506, 'grad_norm': 0.29518041014671326, 'learning_rate': 0.0, 'epoch': 0.4}
{'loss': 2.2094, 'grad_norm': 0.48138654232025146, 'learning_rate': 2.0000000000000003e-06, 'epoch': 0.8}
{'loss': 1.9392, 'grad_norm': 0.32971176505088806, 'learning_rate': 4.000000000000001e-06, 'epoch': 1.0}
{'loss': 1.9839, 'grad_norm': 0.39397770166397095, 'learning_rate': 6e-06, 'epoch': 1.4}
{'loss': 2.0122, 'grad_norm': 0.35957279801368713, 'learning_rate': 8.000000000000001e-06, 'epoch': 1.8}
{'loss': 2.0725, 'grad_norm': 0.3390657603740692, 'learning_rate': 1e-05, 'epoch': 2.0}
{'loss': 1.9451, 'grad_norm': 0.3051750659942627, 'learning_rate': 1.2e-05, 'epoch': 2.4}
{'loss': 1.935, 'grad_norm': 0.36684536933898926, 'learning_rate': 1.4000000000000001e-05, 'epoch': 2.8}
{'loss': 2.4665, 'grad_norm': 0.5520522594451904, 'learning_rate': 1.6000000000000003e-05, 'epoch': 3.0}
{'train_runtime': 26.3052, 'train_samples_per_second': 1.14, 'train_steps_per_second': 0.342, 'train_loss': 2.0571490128835044, 'epoch': 3.0}
