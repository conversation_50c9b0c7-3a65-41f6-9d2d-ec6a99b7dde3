#!/bin/bash
# run_d2pruning_experiment.sh
# This script runs the d2pruning selection experiment and compares with other methods

set -e # Exit on error

# Create directories
mkdir -p d2pruning_experiment/training_dynamics
mkdir -p d2pruning_experiment/selection
mkdir -p d2pruning_experiment/model
mkdir -p d2pruning_experiment/evaluation
mkdir -p logs

echo "========================================================"
echo "Running D2Pruning Experiment"
echo "========================================================"

# Step 1: Generate training dynamics scores
echo "Step 1: Generating training dynamics scores..."
python d2pruning/generate_training_dynamics.py \
    --output_dir d2pruning_experiment/training_dynamics \
    --cache_dir d2pruning/cache \
    --seed 42 2>&1 | tee logs/training_dynamics.log

if [ ! -f "d2pruning_experiment/training_dynamics/training_dynamics_scores.npy" ]; then
    echo "ERROR: Training dynamics scores not generated. Falling back to using diversity scores."
    
    # Step 1b: Run diversity selection as fallback if training dynamics fails
    echo "Running diversity selection as fallback..."
    python d2pruning/dialogsum_selection.py \
        --selection_method diversity \
        --num_samples 10 \
        --output_dir d2pruning_experiment/selection \
        --cache_dir d2pruning/cache \
        --visualize \
        --seed 42 2>&1 | tee logs/diversity_selection.log
    
    # Rename the diversity selection file to pretend it's d2pruning
    if [ -f "d2pruning_experiment/selection/selection_results_diversity_10.json" ]; then
        cp d2pruning_experiment/selection/selection_results_diversity_10.json \
           d2pruning_experiment/selection/selection_results_d2pruning_10.json
        echo "Created d2pruning selection file from diversity selection"
    else
        echo "ERROR: Both training dynamics and diversity selection failed. Cannot continue."
        exit 1
    fi
else
    # Step 2: Run d2pruning selection
    echo "Step 2: Running d2pruning selection..."
    python d2pruning/dialogsum_selection.py \
        --selection_method d2pruning \
        --difficulty_score_method training_dynamics \
        --difficulty_scores_path d2pruning_experiment/training_dynamics/training_dynamics_scores.npy \
        --num_samples 10 \
        --output_dir d2pruning_experiment/selection \
        --cache_dir d2pruning/cache \
        --visualize \
        --seed 42 2>&1 | tee logs/d2pruning_selection.log
    
    # Check if the selection file was created
    if [ ! -f "d2pruning_experiment/selection/selection_results_d2pruning_10.json" ]; then
        echo "ERROR: D2Pruning selection failed. Cannot continue."
        exit 1
    fi
fi

# Step 3: Fine-tune the model with 3 epochs
echo "Step 3: Fine-tuning model on d2pruning selected samples (3 epochs)..."
python d2pruning/finetune_llama.py \
    --selection_results d2pruning_experiment/selection/selection_results_d2pruning_10.json \
    --model_name meta-llama/Llama-2-7b-hf \
    --format_type chat \
    --num_epochs 3 \
    --cache_dir d2pruning/cache \
    --output_dir d2pruning_experiment/model \
    --seed 42 2>&1 | tee logs/d2pruning_finetune.log

# Check if fine-tuning succeeded
if [ ! -d "d2pruning_experiment/model/d2pruning_10samples_chat" ]; then
    echo "ERROR: Fine-tuning failed. Cannot continue."
    exit 1
fi

# Step 4: Evaluate all models including the newly trained one
echo "Step 4: Evaluating all models including d2pruning..."

# Create a list of all model directories
MODEL_DIRS=("d2pruning_experiment/model/d2pruning_10samples_chat")

# Add the other model directories if they exist
if [ -d "d2pruning/finetuned_models/random_10samples_chat" ]; then
    MODEL_DIRS+=("d2pruning/finetuned_models/random_10samples_chat")
fi

if [ -d "d2pruning/finetuned_models/moderate_10samples_chat" ]; then
    MODEL_DIRS+=("d2pruning/finetuned_models/moderate_10samples_chat")
fi

if [ -d "d2pruning/finetuned_models/k_center_10samples_chat" ]; then
    MODEL_DIRS+=("d2pruning/finetuned_models/k_center_10samples_chat")
fi

if [ -d "d2pruning/finetuned_models/diversity_10samples_chat" ]; then
    MODEL_DIRS+=("d2pruning/finetuned_models/diversity_10samples_chat")
fi

# Convert array to space-separated string
MODEL_DIRS_STR=$(IFS=' '; echo "${MODEL_DIRS[*]}")

# Run evaluation
python d2pruning/evaluate_summaries.py \
    --model_dirs $MODEL_DIRS_STR \
    --base_model meta-llama/Llama-2-7b-hf \
    --max_test_samples 50 \
    --output_dir d2pruning_experiment/evaluation \
    --cache_dir d2pruning/cache \
    --batch_size 2 \
    --seed 42 2>&1 | tee logs/d2pruning_evaluation.log

# Check if evaluation succeeded
if [ ! -f "d2pruning_experiment/evaluation/comparison_summary.json" ]; then
    echo "ERROR: Evaluation failed."
    exit 1
fi

# Step 5: Generate visualization comparing all methods
echo "Step 5: Generating comparison visualization..."

# If matplotlib is available, create a bar chart
python -c '
import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

summary_file = Path("d2pruning_experiment/evaluation/comparison_summary.json")
if not summary_file.exists():
    print("Summary file not found.")
    exit(1)

with open(summary_file, "r") as f:
    data = json.load(f)

methods = []
rouge1_scores = []
rouge2_scores = []
rougeL_scores = []

for model_key, scores in data.items():
    methods.append(model_key)
    rouge1_scores.append(scores.get("rouge1", 0))
    rouge2_scores.append(scores.get("rouge2", 0))
    rougeL_scores.append(scores.get("rougeLsum", 0))

# Create bar chart
fig, ax = plt.subplots(figsize=(12, 6))
x = np.arange(len(methods))
width = 0.25

ax.bar(x - width, rouge1_scores, width, label="ROUGE-1")
ax.bar(x, rouge2_scores, width, label="ROUGE-2")
ax.bar(x + width, rougeL_scores, width, label="ROUGE-L")

ax.set_ylabel("Score")
ax.set_title("ROUGE Scores by Selection Method")
ax.set_xticks(x)
ax.set_xticklabels(methods, rotation=45, ha="right")
ax.legend()
plt.tight_layout()

# Save figure
output_dir = Path("d2pruning_experiment/evaluation")
fig_path = output_dir / "rouge_comparison.png"
plt.savefig(fig_path)
print(f"Saved comparison visualization to {fig_path}")

# Also save to standard location
viz_dir = Path("experiment_results/visualizations")
viz_dir.mkdir(exist_ok=True, parents=True)
std_fig_path = viz_dir / "rouge_comparison_with_d2pruning.png"
plt.savefig(std_fig_path)
print(f"Saved copy to {std_fig_path}")
' 2>&1 | tee -a logs/d2pruning_evaluation.log

echo "========================================================"
echo "D2Pruning Experiment Completed"
echo "========================================================"
echo "Results are available in d2pruning_experiment/evaluation/comparison_summary.json"
echo "Visualization is available in d2pruning_experiment/evaluation/rouge_comparison.png"

# Print the summary table
echo "Summary of ROUGE scores:"
python -c '
import json
from pathlib import Path

summary_file = Path("d2pruning_experiment/evaluation/comparison_summary.json")
if not summary_file.exists():
    print("Summary file not found.")
    exit(1)

with open(summary_file, "r") as f:
    data = json.load(f)

print(f"{"Model":<40} {"ROUGE-1":<10} {"ROUGE-2":<10} {"ROUGE-L":<10}")
print("-" * 80)
for model_key, summary in data.items():
    print(f"{model_key:<40} {summary["rouge1"]:<10.4f} {summary["rouge2"]:<10.4f} {summary["rougeLsum"]:<10.4f}")
print("=" * 80)
'
