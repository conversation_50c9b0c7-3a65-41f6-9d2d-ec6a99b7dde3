07/24/2025 19:42:59 - INFO - wandb_logger - Initialized W&B run: selection_diversity_10
07/24/2025 19:42:59 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/k7gsct1b
07/24/2025 19:42:59 - INFO - __main__ - Starting DialogSum data selection pipeline
07/24/2025 19:42:59 - INFO - __main__ - Selection method: diversity
07/24/2025 19:42:59 - INFO - __main__ - Number of samples: 10
07/24/2025 19:42:59 - INFO - __main__ - Embedding model: all-MiniLM-L6-v2
07/24/2025 19:42:59 - INFO - __main__ - Loading DialogSum dataset...
07/24/2025 19:42:59 - INFO - __main__ - Loading cached DialogSum train dataset from cache/dialogsum_train.pkl
07/24/2025 19:42:59 - INFO - __main__ - Loading cached DialogSum test dataset from cache/dialogsum_test.pkl
07/24/2025 19:42:59 - INFO - __main__ - Train dataset size: 12460
07/24/2025 19:42:59 - INFO - __main__ - Test dataset size: 1500
07/24/2025 19:43:00 - INFO - __main__ - Preprocessed 12460 training samples
07/24/2025 19:43:00 - INFO - __main__ - Average dialogue length: 131.0 words
07/24/2025 19:43:00 - INFO - __main__ - Average summary length: 22.9 words
07/24/2025 19:43:00 - INFO - wandb_logger - Logged dataset information to W&B
07/24/2025 19:43:00 - INFO - __main__ - Extracting embeddings...
07/24/2025 19:43:00 - INFO - __main__ - Loading cached embeddings from cache/embeddings_dialogsum_train_combined_all-MiniLM-L6-v2.npy
07/24/2025 19:43:00 - INFO - __main__ - Extracted embeddings shape: (12460, 384)
07/24/2025 19:43:00 - INFO - wandb_logger - Logged embedding information to W&B
07/24/2025 19:43:00 - INFO - __main__ - Performing diversity selection...
07/24/2025 19:43:00 - INFO - __main__ - Selection completed in 0.22 seconds
07/24/2025 19:43:00 - INFO - __main__ - Selected indices: [6924 8387 9348 8627 9825 3034 9269 9536 8049 8874]
07/24/2025 19:43:00 - INFO - wandb_logger - Logged selection results for diversity to W&B
