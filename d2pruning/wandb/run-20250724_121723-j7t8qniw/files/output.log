07/24/2025 12:17:26 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_instruction_20250724_121722
07/24/2025 12:17:26 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/j7t8qniw
07/24/2025 12:17:26 - INFO - __main__ - ============================================================
07/24/2025 12:17:26 - INFO - __main__ - STEP 1: DATA SELECTION
07/24/2025 12:17:26 - INFO - __main__ - ============================================================
07/24/2025 12:17:26 - INFO - __main__ - Running data selection with method: random
07/24/2025 12:17:26 - INFO - __main__ - Selection results already exist: results/selection_results_random_10.json
07/24/2025 12:17:26 - INFO - __main__ - Running data selection with method: moderate
07/24/2025 12:17:26 - INFO - __main__ - Selection results already exist: results/selection_results_moderate_10.json
07/24/2025 12:17:26 - INFO - __main__ - Running data selection with method: k_center
07/24/2025 12:17:26 - INFO - __main__ - Selection results already exist: results/selection_results_k_center_10.json
07/24/2025 12:17:26 - INFO - __main__ - ============================================================
07/24/2025 12:17:26 - INFO - __main__ - STEP 2: FINE-TUNING
07/24/2025 12:17:26 - INFO - __main__ - ============================================================
07/24/2025 12:17:26 - INFO - __main__ - Running fine-tuning for results/selection_results_random_10.json
07/24/2025 12:17:26 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_random_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_random_10
07/24/2025 12:20:01 - INFO - __main__ - Fine-tuning completed: finetuned_models/random_10samples_instruction
07/24/2025 12:20:01 - INFO - __main__ - Running fine-tuning for results/selection_results_moderate_10.json
07/24/2025 12:20:01 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_moderate_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_moderate_10
07/24/2025 12:22:45 - INFO - __main__ - Fine-tuning completed: finetuned_models/moderate_10samples_instruction
07/24/2025 12:22:45 - INFO - __main__ - Running fine-tuning for results/selection_results_k_center_10.json
07/24/2025 12:22:45 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_k_center_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_k_center_10
07/24/2025 12:25:26 - INFO - __main__ - Fine-tuning completed: finetuned_models/k_center_10samples_instruction
07/24/2025 12:25:26 - INFO - __main__ - ============================================================
07/24/2025 12:25:26 - INFO - __main__ - STEP 3: EVALUATION
07/24/2025 12:25:26 - INFO - __main__ - ============================================================
07/24/2025 12:25:26 - INFO - __main__ - Running evaluation on all models
07/24/2025 12:25:26 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python evaluate_summaries.py --model_dirs finetuned_models/random_10samples_instruction finetuned_models/moderate_10samples_instruction finetuned_models/k_center_10samples_instruction --base_model meta-llama/Llama-2-7b-hf --max_test_samples 100 --output_dir evaluation_results --cache_dir cache --enable_wandb --wandb_run_name eval_3models
[34m[1mwandb[0m: [33mWARNING[0m Fatal error while uploading data. Some run data will not be synced, but it will still be written to disk. Use `wandb sync` at the end of the run to try uploading.
07/24/2025 13:54:37 - INFO - __main__ - Evaluation completed: evaluation_results
07/24/2025 13:54:38 - INFO - wandb_logger - Logged comparison summary to W&B
07/24/2025 13:54:38 - INFO - wandb_logger - Logged experiment completion to W&B
07/24/2025 13:54:38 - INFO - wandb_logger - Saved CSV summary to experiment_results.csv
07/24/2025 13:54:39 - INFO - wandb_logger - Saved artifact comparison_summary: evaluation_results/comparison_summary.json
07/24/2025 13:54:39 - INFO - wandb_logger - Saved artifact results_csv: experiment_results.csv
