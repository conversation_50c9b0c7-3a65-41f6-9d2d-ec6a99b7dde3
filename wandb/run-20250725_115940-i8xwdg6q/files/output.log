07/25/2025 11:59:42 - INFO - wandb_logger - Initialized W&B run: finetune_random_10
07/25/2025 11:59:42 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/i8xwdg6q
07/25/2025 11:59:42 - INFO - __main__ - W&B logging initialized with run name: finetune_random_10
07/25/2025 11:59:42 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 11:59:45 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]Exception ignored in: <generator object tqdm.__iter__ at 0x7aae4615e490>Exception ignored in sys.unraisablehook: <built-in function unraisablehook>
