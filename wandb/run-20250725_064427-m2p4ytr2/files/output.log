07/25/2025 06:44:29 - INFO - __main__ - Loading model from d2pruning/finetuned_models/diversity_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 06:44:31 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:32<00:00, 16.15s/it]
07/25/2025 06:45:06 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 06:45:06 - INFO - __main__ - Starting summary generation...
Generating Summaries:   0%|          | 0/13 [00:00<?, ?it/s]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/bitsandbytes/nn/modules.py:457: UserWarning: Input type into Linear4bit is torch.float16, but bnb_4bit_compute_dtype=torch.float32 (default). This will lead to slow inference or training speed.
  warnings.warn(
Generating Summaries:   8%|▊         | 1/13 [00:40<08:08, 40.74s/it]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
