{"selection_method": "moderate", "num_samples": 10, "format_type": "alpaca", "model_name": "meta-llama/Llama-2-7b-hf", "training_results": {"training_time": 21.02921962738037, "num_epochs": 3, "learning_rate": 0.0002, "num_samples": 10, "final_loss": 1.8166184425354004, "total_parameters": 3517190144, "trainable_parameters": 16777216}, "output_dir": "finetuned_models/moderate_10samples_alpaca", "evaluation_results": {"rouge_scores": {"rouge1": 0.07986296135152471, "rouge2": 0.015645258832758833, "rougeL": 0.06844003480882599, "rougeLsum": 0.06852852461002484}, "generation_time": 1470.2965412139893, "num_test_samples": 100, "generated_summaries": ["### Hints", "", "Your response should be in the form of a memo to all employees, informing them of the new policy. You may include a warning of the consequences of violating the policy, but do not include the specific details of the policy.", "#Person1#: You're finally here! What took so long?", "### Hints:"], "reference_summaries": ["Ms<PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "Ms<PERSON> <PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations."]}}