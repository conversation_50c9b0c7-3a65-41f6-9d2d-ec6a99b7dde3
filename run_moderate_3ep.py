#!/usr/bin/env python3
"""
Run moderate selection and evaluation with 3 epochs of fine-tuning.
This script is designed to be more robust by explicitly handling each step.
"""

import os
import sys
import argparse
import logging
import subprocess
import json
import time
from pathlib import Path

# Set up logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

def run_command(cmd, desc=None, check=True):
    """Run a command and log its output"""
    if desc:
        logger.info(f"Running {desc}...")
    
    cmd_str = " ".join(cmd)
    logger.info(f"Command: {cmd_str}")
    
    start_time = time.time()
    process = subprocess.run(cmd, capture_output=True, text=True)
    elapsed = time.time() - start_time
    
    if process.returncode != 0:
        logger.error(f"Command failed with code {process.returncode}")
        logger.error(f"Stderr: {process.stderr}")
        
        if check:
            raise RuntimeError(f"Command failed: {cmd_str}")
        
    logger.info(f"Command completed in {elapsed:.2f} seconds")
    
    # Always log stdout for debugging
    if process.stdout:
        logger.info(f"Command stdout: {process.stdout[:500]}...")
        
    return process

def main():
    parser = argparse.ArgumentParser(description="Run moderate selection with 3-epoch fine-tuning")
    parser.add_argument("--num_samples", type=int, default=10, 
                        help="Number of samples to select")
    parser.add_argument("--output_dir", type=str, default="./moderate_3ep_results",
                        help="Output directory")
    parser.add_argument("--cache_dir", type=str, default="./d2pruning/cache",
                        help="Cache directory")
    parser.add_argument("--base_model", type=str, default="meta-llama/Llama-2-7b-hf",
                        help="Base model name")
    parser.add_argument("--max_test_samples", type=int, default=50,
                        help="Maximum number of test samples to evaluate")
    parser.add_argument("--batch_size", type=int, default=2, 
                        help="Batch size for inference")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")

    args = parser.parse_args()
    
    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    selection_dir = output_dir / "selection"
    selection_dir.mkdir(exist_ok=True)
    
    model_dir = output_dir / "model"
    model_dir.mkdir(exist_ok=True)
    
    eval_dir = output_dir / "evaluation"
    eval_dir.mkdir(exist_ok=True)

    # Step 1: Generate embeddings explicitly first
    logger.info("Generating embeddings for selection...")
    embed_cmd = [
        sys.executable,
        "-c", 
        "from sentence_transformers import SentenceTransformer; "
        "from datasets import load_dataset; "
        f"model = SentenceTransformer('all-mpnet-base-v2'); "
        f"dataset = load_dataset('knkarthick/dialogsum', split='train', cache_dir='{args.cache_dir}'); "
        f"texts = ['Dialogue: ' + item['dialogue'] + '\\nSummary: ' + item['summary'] for item in dataset]; "
        f"embeddings = model.encode(texts, batch_size=32, show_progress_bar=True); "
        f"import numpy as np; "
        f"np.save('{args.cache_dir}/embeddings_dialogsum_train_combined_all-mpnet-base-v2.npy', embeddings); "
        "print('Embeddings generated successfully');"
    ]
    
    try:
        embed_process = run_command(embed_cmd, "Embedding generation", check=False)
        if embed_process.returncode != 0:
            logger.warning("Embedding generation failed, but continuing as they may already exist")
    except Exception as e:
        logger.error(f"Error during embedding generation: {e}")
        logger.warning("Continuing as embeddings may already exist")

    # Step 2: Run selection with moderate method (more robust)
    logger.info("Running selection with moderate method...")
    selection_cmd = [
        sys.executable,
        "d2pruning/dialogsum_selection.py",
        "--selection_method", "moderate",
        "--num_samples", str(args.num_samples),
        "--output_dir", str(selection_dir),
        "--cache_dir", args.cache_dir,
        "--seed", str(args.seed),
        "--visualize"
    ]
    
    selection_process = run_command(selection_cmd, "Moderate selection")
    
    # Check if selection was successful
    selection_file = selection_dir / f"selection_results_moderate_{args.num_samples}.json"
    if not selection_file.exists():
        logger.error(f"Selection file not found: {selection_file}")
        
        # Try random selection as a last resort
        logger.info("Falling back to random selection...")
        random_cmd = [
            sys.executable,
            "d2pruning/dialogsum_selection.py",
            "--selection_method", "random",
            "--num_samples", str(args.num_samples),
            "--output_dir", str(selection_dir),
            "--cache_dir", args.cache_dir,
            "--seed", str(args.seed)
        ]
        
        random_process = run_command(random_cmd, "Random selection (fallback)")
        selection_file = selection_dir / f"selection_results_random_{args.num_samples}.json"
    
    # Make sure we have a selection file
    if not selection_file.exists():
        logger.error("All selection methods failed. Cannot continue.")
        sys.exit(1)
    
    logger.info(f"Using selection file: {selection_file}")
    
    # Step 3: Run fine-tuning with 3 epochs
    logger.info("Running fine-tuning with 3 epochs...")
    finetune_cmd = [
        sys.executable,
        "d2pruning/finetune_llama.py",
        "--selection_results", str(selection_file),
        "--model_name", args.base_model,
        "--format_type", "chat",
        "--num_epochs", "3",  # Use 3 epochs as requested
        "--output_dir", str(model_dir),
        "--cache_dir", args.cache_dir,
        "--seed", str(args.seed)
    ]
    
    finetune_process = run_command(finetune_cmd, "Fine-tuning (3 epochs)")
    
    # Step 4: Run evaluation
    # Extract selection method from filename
    selection_method = selection_file.stem.split('_')[2]
    finetuned_model_path = model_dir / f"{selection_method}_{args.num_samples}samples_chat"
    
    if not finetuned_model_path.exists():
        logger.error(f"Fine-tuned model not found at {finetuned_model_path}")
        logger.error("Fine-tuning failed. Cannot continue to evaluation.")
        sys.exit(1)
    
    logger.info("Running evaluation...")
    eval_cmd = [
        sys.executable,
        "d2pruning/evaluate_summaries.py",
        "--model_dirs", str(finetuned_model_path),
        "--base_model", args.base_model,
        "--max_test_samples", str(args.max_test_samples),
        "--output_dir", str(eval_dir),
        "--cache_dir", args.cache_dir,
        "--batch_size", str(args.batch_size),
        "--seed", str(args.seed)
    ]
    
    eval_process = run_command(eval_cmd, "Evaluation")
    
    # Print evaluation results
    eval_results_path = eval_dir / "comparison_summary.json"
    if eval_results_path.exists():
        with open(eval_results_path, 'r') as f:
            results = json.load(f)
        
        logger.info("\n===== EVALUATION RESULTS =====")
        for model_name, scores in results.items():
            logger.info(f"\nModel: {model_name}")
            logger.info(f"Selection Method: {scores['selection_method']}")
            logger.info(f"Num Samples: {scores['num_samples']}")
            logger.info(f"ROUGE-1: {scores['rouge1']:.4f}")
            logger.info(f"ROUGE-2: {scores['rouge2']:.4f}")
            logger.info(f"ROUGE-L: {scores['rougeL']:.4f}")
            logger.info(f"ROUGE-Lsum: {scores['rougeLsum']:.4f}")
    else:
        logger.warning(f"Evaluation results not found at {eval_results_path}")
    
    logger.info(f"\nExperiment complete. Results available in {output_dir}")

if __name__ == "__main__":
    main()
