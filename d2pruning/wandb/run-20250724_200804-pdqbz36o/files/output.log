07/24/2025 20:08:06 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_chat_20250724_200803
07/24/2025 20:08:06 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/pdqbz36o
07/24/2025 20:08:06 - INFO - __main__ - ============================================================
07/24/2025 20:08:06 - INFO - __main__ - STEP 1: DATA SELECTION
07/24/2025 20:08:06 - INFO - __main__ - ============================================================
07/24/2025 20:08:06 - INFO - __main__ - Running data selection with method: random
07/24/2025 20:08:06 - INFO - __main__ - Regenerating selection results with new embedding model: meta-llama/Llama-2-7b-hf
07/24/2025 20:08:06 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python dialogsum_selection.py --num_samples 10 --selection_method random --embedding_model meta-llama/Llama-2-7b-hf --cache_dir cache --output_dir results --enable_wandb --wandb_run_name selection_random_10
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 441, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 422, in main
    experiment_summary = experiment.run_complete_experiment(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 226, in run_complete_experiment
    results_file = self.run_data_selection(method, num_samples,
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 95, in run_data_selection
    result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
  File "/usr/lib/python3.10/subprocess.py", line 505, in run
    stdout, stderr = process.communicate(input, timeout=timeout)
  File "/usr/lib/python3.10/subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "/usr/lib/python3.10/subprocess.py", line 2021, in _communicate
    ready = selector.select(timeout)
  File "/usr/lib/python3.10/selectors.py", line 416, in select
    fd_event_list = self._selector.poll(timeout)
KeyboardInterrupt
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 441, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 422, in main
    experiment_summary = experiment.run_complete_experiment(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 226, in run_complete_experiment
    results_file = self.run_data_selection(method, num_samples,
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 95, in run_data_selection
    result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
  File "/usr/lib/python3.10/subprocess.py", line 505, in run
    stdout, stderr = process.communicate(input, timeout=timeout)
  File "/usr/lib/python3.10/subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "/usr/lib/python3.10/subprocess.py", line 2021, in _communicate
    ready = selector.select(timeout)
  File "/usr/lib/python3.10/selectors.py", line 416, in select
    fd_event_list = self._selector.poll(timeout)
KeyboardInterrupt
Exception ignored in atexit callback: <function _start_and_connect_service.<locals>.teardown_atexit at 0x79cd7ec69090>
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/service/service_connection.py", line 54, in teardown_atexit
    conn.teardown(hooks.exit_code)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/service/service_connection.py", line 182, in teardown
    self._router.join()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/router.py", line 75, in join
    self._thread.join()
  File "/usr/lib/python3.10/threading.py", line 1096, in join
    self._wait_for_tstate_lock()
  File "/usr/lib/python3.10/threading.py", line 1116, in _wait_for_tstate_lock
    if lock.acquire(block, timeout):
KeyboardInterrupt:
