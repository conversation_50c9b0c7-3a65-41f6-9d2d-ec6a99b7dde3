#!/usr/bin/env python3
"""
DialogSum Evaluation Script - Compatible with existing setup

This script evaluates DialogSum models using the same format as your previous setup
while being consistent with the existing DialogSum evaluation infrastructure.
Fixes wandb logging issues and provides proper error handling.
"""

import os
import sys
import json
import time
import logging
import argparse
import random
import socket
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional
from itertools import chain

import torch
from datasets import load_dataset, load_from_disk
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoModelForSeq2SeqLM, set_seed
from torch.utils.data import DataLoader
from tqdm import tqdm
import evaluate

# Set up logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

# Disable tokenizer parallelism warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"


def get_device():
    """Get the best available device."""
    if torch.cuda.is_available():
        return torch.device("cuda")
    elif torch.backends.mps.is_available() and torch.backends.mps.is_built():
        return torch.device("mps")
    else:
        return torch.device("cpu")


def set_seed_for_reproducibility(seed: int):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    set_seed(seed)
    logger.info(f"Random seed set to {seed} for reproducibility")


def create_dialogsum_prompt(dialogue: str, task_type: str = "summary_dialogue") -> str:
    """Create prompt for DialogSum evaluation."""
    if task_type == "summary_dialogue":
        return f"Summarize the following dialogue:\n\n{dialogue}\n\nSummary:"
    else:
        return f"### Instruction:\nSummarize the following dialogue:\n\n### Input:\n{dialogue}\n\n### Response:\n"


def tokenization(items, tokenizer):
    """Tokenize items for batch processing."""
    return tokenizer(items["prompt"], padding='longest', truncation=True, max_length=512)


class DialogSumEvaluator:
    """Evaluator for DialogSum models with proper wandb integration."""
    
    def __init__(self, cache_dir: str = "./cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.rouge_metric = evaluate.load("rouge")
        self.wandb_instance = None
        
    def initialize_wandb(self, wandb_api_key: str, model_id: str, dataset_id: str, config: Dict[str, Any]):
        """Initialize wandb with proper error handling."""
        if not wandb_api_key:
            logger.warning("No wandb API key provided, skipping wandb logging")
            return
            
        try:
            import wandb
            os.environ["WANDB_API_KEY"] = wandb_api_key
            
            # Create a unique run name
            run_name = f"{model_id.replace('/', '_')}_{dataset_id.replace('/', '_')}_{int(time.time())}"
            
            self.wandb_instance = wandb
            self.wandb_instance.init(
                project="dialogsum-benchmark",
                name=run_name,
                config=config,
                tags=["dialogsum", "evaluation", "summarization"],
                notes=f"DialogSum evaluation on host: {socket.gethostname()}",
                reinit=True
            )
            logger.info(f"Initialized wandb run: {run_name}")
            logger.info(f"Wandb URL: {self.wandb_instance.run.url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize wandb: {e}")
            logger.warning("Continuing without wandb logging")
            self.wandb_instance = None
    
    def safe_wandb_log(self, metrics: Dict[str, Any]):
        """Safely log metrics to wandb."""
        if self.wandb_instance is None:
            return
            
        try:
            self.wandb_instance.log(metrics)
        except Exception as e:
            logger.warning(f"Failed to log to wandb: {e}")
    
    def load_model_and_tokenizer(self, model_id: str, model_tokenizer: str = None, 
                                 use_bfloat16: bool = False, seq2seq: bool = False):
        """Load model and tokenizer."""
        device = get_device()
        logger.info(f"Loading model on device: {device}")
        
        # Load tokenizer
        tokenizer_id = model_tokenizer if model_tokenizer else model_id
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_id)
        tokenizer.add_special_tokens({"pad_token": tokenizer.eos_token})
        tokenizer.padding_side = 'left'
        
        # Load model
        model_kwargs = {}
        if use_bfloat16 and device.type != "cpu":
            model_kwargs["torch_dtype"] = torch.bfloat16
            
        if seq2seq:
            model = AutoModelForSeq2SeqLM.from_pretrained(model_id, **model_kwargs)
        else:
            model = AutoModelForCausalLM.from_pretrained(model_id, **model_kwargs)
            
        model = model.to(device)
        model.resize_token_embeddings(len(tokenizer))
        model.config.pad_token_id = tokenizer.pad_token_id
        model.eval()
        
        logger.info("Model and tokenizer loaded successfully")
        return model, tokenizer, device
    
    def prepare_dataset(self, dataset_id: str, split_name: str = "test", 
                       from_disk: bool = False, max_samples: int = None,
                       context_length: int = None, seed: int = 42):
        """Prepare dataset for evaluation."""
        logger.info("Loading and preparing dataset...")
        
        # Load dataset
        if from_disk:
            dataset = load_from_disk(dataset_id)
            if split_name:
                dataset = dataset[split_name]
        else:
            dataset = load_dataset(dataset_id, split=split_name, cache_dir=self.cache_dir)
        
        # For DialogSum, ensure we have the right column names
        if dataset_id == "knkarthick/dialogsum" or "dialogsum" in dataset_id.lower():
            # Map to expected format
            def format_dialogsum(item):
                item['context'] = item['dialogue']
                item['answers'] = item['summary']
                return item
            dataset = dataset.map(format_dialogsum)
        
        # Create prompts
        def create_prompt_column(item):
            item['prompt'] = create_dialogsum_prompt(item['context'])
            return item
        
        dataset = dataset.map(create_prompt_column)
        
        # Limit samples if specified
        if max_samples and len(dataset) > max_samples:
            dataset = dataset.shuffle(seed=seed)
            dataset = dataset.select(range(max_samples))
            logger.info(f"Limited dataset to {max_samples} samples")
        
        # Filter by context length if specified
        if context_length:
            original_len = len(dataset)
            dataset = dataset.filter(lambda item: len(item['context'].split()) <= context_length)
            logger.info(f"Filtered dataset from {original_len} to {len(dataset)} samples based on context length")
        
        logger.info(f"Dataset prepared with {len(dataset)} samples")
        return dataset

    def generate_summaries(self, model, tokenizer, dataset, device, batch_size: int = 4,
                          num_workers: int = 2, seq2seq: bool = False):
        """Generate summaries for the dataset."""
        logger.info("Starting summary generation...")

        # Tokenize dataset
        def tokenize_batch(items):
            return tokenization(items, tokenizer)

        tokenized_dataset = dataset.map(tokenize_batch, batched=True, batch_size=batch_size)
        tokenized_dataset.set_format(type="torch", columns=["input_ids", "attention_mask"])

        dataloader = DataLoader(tokenized_dataset, batch_size=batch_size, num_workers=num_workers)

        predictions = []
        step = 0

        with torch.no_grad():
            for batch in tqdm(dataloader, desc="Generating summaries"):
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)

                # Generate summaries
                output = model.generate(
                    input_ids,
                    attention_mask=attention_mask,
                    max_new_tokens=150,
                    do_sample=False,
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id
                )

                # For causal LM, remove input tokens from output
                if not seq2seq:
                    generated_sequences = []
                    for i in range(output.shape[0]):
                        input_length = (input_ids[i] != tokenizer.pad_token_id).sum().item()
                        generated_part = output[i, input_length:]
                        generated_sequences.append(generated_part)

                    # Pad sequences for batch decoding
                    max_length = max(len(seq) for seq in generated_sequences)
                    padded_sequences = []
                    for seq in generated_sequences:
                        if len(seq) < max_length:
                            padding = torch.full((max_length - len(seq),), tokenizer.pad_token_id,
                                               dtype=seq.dtype, device=seq.device)
                            seq = torch.cat([seq, padding])
                        padded_sequences.append(seq.unsqueeze(0))

                    output = torch.cat(padded_sequences, dim=0)

                # Decode generated text
                sentences = tokenizer.batch_decode(output, skip_special_tokens=True)

                # Clean up generated text
                cleaned_sentences = []
                for text in sentences:
                    text = text.strip()

                    # Remove leading newlines
                    if text.startswith('\n'):
                        text = text[1:].strip()

                    # Take first meaningful line
                    lines = text.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and len(line) > 1 and line not in [':', ';', '.', ',', '!', '?']:
                            text = line
                            break
                    else:
                        # If no meaningful line found, take first non-empty line
                        for line in lines:
                            line = line.strip()
                            if line:
                                text = line
                                break
                        else:
                            text = "No summary generated."

                    cleaned_sentences.append(text)

                predictions.extend(cleaned_sentences)
                step += len(cleaned_sentences)

                # Live wandb logging every 10 examples
                if self.wandb_instance and step % 10 == 0:
                    try:
                        # Get partial references for live metrics
                        references = [dataset[i]['answers'] for i in range(min(step, len(dataset)))]
                        partial_predictions = predictions[:len(references)]

                        # Compute partial ROUGE scores
                        partial_rouge = self.rouge_metric.compute(
                            predictions=partial_predictions,
                            references=references,
                            use_stemmer=True
                        )

                        live_metrics = {
                            "live/rouge1": partial_rouge['rouge1'],
                            "live/rouge2": partial_rouge['rouge2'],
                            "live/rougeL": partial_rouge['rougeL'],
                            "live/step": step,
                            "live/samples": len(partial_predictions)
                        }

                        self.safe_wandb_log(live_metrics)
                        logger.info(f"Live metrics at step {step}: ROUGE-1={partial_rouge['rouge1']:.4f}")

                    except Exception as e:
                        logger.warning(f"Failed to compute live metrics: {e}")

        logger.info(f"Generated {len(predictions)} summaries")
        return predictions

    def compute_metrics(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """Compute evaluation metrics."""
        logger.info("Computing evaluation metrics...")

        # Ensure predictions and references have same length
        min_len = min(len(predictions), len(references))
        predictions = predictions[:min_len]
        references = references[:min_len]

        # Clean predictions and references
        predictions = [pred.strip() if pred.strip() else "No summary generated." for pred in predictions]
        references = [ref.strip() for ref in references]

        # Compute ROUGE scores
        rouge_scores = self.rouge_metric.compute(
            predictions=predictions,
            references=references,
            use_stemmer=True
        )

        # Extract and format results
        results = {
            'rouge1': rouge_scores['rouge1'],
            'rouge2': rouge_scores['rouge2'],
            'rougeL': rouge_scores['rougeL'],
            'rougeLsum': rouge_scores['rougeLsum'],
            'num_samples': len(predictions)
        }

        # Convert to percentages for consistency
        for key in ['rouge1', 'rouge2', 'rougeL', 'rougeLsum']:
            results[key] = round(results[key] * 100, 2)

        logger.info("Evaluation metrics computed:")
        for metric, score in results.items():
            if metric != 'num_samples':
                logger.info(f"  {metric}: {score:.2f}")

        return results

    def save_results(self, results: Dict[str, Any], predictions: List[str],
                    references: List[str], output_path: str, args):
        """Save evaluation results."""
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Save main results
        results_data = {
            "model": args.model_id,
            "dataset": args.dataset_id,
            "num_samples": len(predictions),
            "evaluation_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            **results
        }

        results_file = output_dir / f"evaluation_results_{args.seed}.json"
        with open(results_file, 'w') as f:
            json.dump(results_data, f, indent=4)

        logger.info(f"Results saved to {results_file}")

        # Save predictions if requested
        if args.save_predictions:
            predictions_data = []
            for i, (pred, ref) in enumerate(zip(predictions, references)):
                predictions_data.append({
                    'index': i,
                    'prediction': pred,
                    'reference': ref
                })

            predictions_file = output_dir / f"predictions_{args.seed}.json"
            with open(predictions_file, 'w') as f:
                json.dump(predictions_data, f, indent=2)

            logger.info(f"Predictions saved to {predictions_file}")

    def finish_wandb(self, final_results: Dict[str, Any]):
        """Finish wandb logging."""
        if self.wandb_instance is None:
            return

        try:
            # Log final results
            final_metrics = {f"final/{k}": v for k, v in final_results.items() if isinstance(v, (int, float))}
            self.safe_wandb_log(final_metrics)

            # Finish the run
            self.wandb_instance.finish()
            logger.info("Wandb logging finished")

        except Exception as e:
            logger.warning(f"Failed to finish wandb logging: {e}")


def main():
    parser = argparse.ArgumentParser(description="DialogSum Model Evaluation")
    parser.add_argument("--model_id", type=str, required=True, help="Model ID or path")
    parser.add_argument("--model_tokenizer", type=str, help="Tokenizer ID (default: same as model)")
    parser.add_argument("--dataset_id", type=str, default="knkarthick/dialogsum", help="Dataset ID")
    parser.add_argument("--split_name", type=str, default="test", help="Dataset split")
    parser.add_argument("--from_disk", action="store_true", help="Load dataset from disk")
    parser.add_argument("--max_samples", type=int, help="Maximum samples to evaluate")
    parser.add_argument("--context_length", type=int, help="Maximum context length")
    parser.add_argument("--batch_size", type=int, default=4, help="Batch size")
    parser.add_argument("--num_workers", type=int, default=2, help="Number of workers")
    parser.add_argument("--bfloat16", action="store_true", help="Use bfloat16 precision")
    parser.add_argument("--seq2seq", action="store_true", help="Use seq2seq model")
    parser.add_argument("--output_path", type=str, help="Output directory")
    parser.add_argument("--save_predictions", action="store_true", help="Save predictions")
    parser.add_argument("--wandb_api_key", type=str, help="Wandb API key")
    parser.add_argument("--seed", type=int, default=42, help="Random seed")
    parser.add_argument("--cache_dir", type=str, default="./cache", help="Cache directory")

    args = parser.parse_args()

    # Set seed for reproducibility
    set_seed_for_reproducibility(args.seed)

    # Initialize evaluator
    evaluator = DialogSumEvaluator(cache_dir=args.cache_dir)

    # Initialize wandb if API key provided
    if args.wandb_api_key:
        config = vars(args)
        evaluator.initialize_wandb(args.wandb_api_key, args.model_id, args.dataset_id, config)

    try:
        # Load model and tokenizer
        model, tokenizer, device = evaluator.load_model_and_tokenizer(
            args.model_id, args.model_tokenizer, args.bfloat16, args.seq2seq
        )

        # Prepare dataset
        dataset = evaluator.prepare_dataset(
            args.dataset_id, args.split_name, args.from_disk,
            args.max_samples, args.context_length, args.seed
        )

        # Generate summaries
        predictions = evaluator.generate_summaries(
            model, tokenizer, dataset, device, args.batch_size,
            args.num_workers, args.seq2seq
        )

        # Get references
        references = [dataset[i]['answers'] for i in range(len(predictions))]

        # Compute metrics
        results = evaluator.compute_metrics(predictions, references)

        # Log final results to wandb
        evaluator.safe_wandb_log({f"final/{k}": v for k, v in results.items() if isinstance(v, (int, float))})

        # Save results
        output_path = args.output_path or f"./results/{args.model_id.split('/')[-1]}"
        evaluator.save_results(results, predictions, references, output_path, args)

        # Print summary
        logger.info("="*60)
        logger.info("EVALUATION COMPLETED")
        logger.info("="*60)
        logger.info(f"Model: {args.model_id}")
        logger.info(f"Dataset: {args.dataset_id}")
        logger.info(f"Samples: {results['num_samples']}")
        logger.info("ROUGE Scores:")
        for metric in ['rouge1', 'rouge2', 'rougeL', 'rougeLsum']:
            logger.info(f"  {metric}: {results[metric]:.2f}")

        # Finish wandb
        evaluator.finish_wandb(results)

    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        evaluator.finish_wandb({})
        raise


if __name__ == "__main__":
    main()
