2025-07-24 12:25:46,916 INFO    MainThread:2071116 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-24 12:25:46,916 INFO    MainThread:2071116 [wandb_setup.py:_flush():80] Configure stats pid to 2071116
2025-07-24 12:25:46,916 INFO    MainThread:2071116 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-24 12:25:46,916 INFO    MainThread:2071116 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-24 12:25:46,916 INFO    MainThread:2071116 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-24 12:25:46,917 INFO    MainThread:2071116 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_122546-rsbgmv10/logs/debug.log
2025-07-24 12:25:46,917 INFO    MainThread:2071116 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_122546-rsbgmv10/logs/debug-internal.log
2025-07-24 12:25:46,917 INFO    MainThread:2071116 [wandb_init.py:init():830] calling init triggers
2025-07-24 12:25:46,917 INFO    MainThread:2071116 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'base_model': 'meta-llama/Llama-2-7b-hf', 'max_test_samples': 100, 'model_dirs': ['finetuned_models/random_10samples_instruction', 'finetuned_models/moderate_10samples_instruction', 'finetuned_models/k_center_10samples_instruction'], 'task': 'evaluation', '_wandb': {}}
2025-07-24 12:25:46,917 INFO    MainThread:2071116 [wandb_init.py:init():871] starting backend
2025-07-24 12:25:46,955 INFO    MainThread:2071116 [wandb_init.py:init():874] sending inform_init request
2025-07-24 12:25:46,973 INFO    MainThread:2071116 [wandb_init.py:init():882] backend started and connected
2025-07-24 12:25:46,977 INFO    MainThread:2071116 [wandb_init.py:init():953] updated telemetry
2025-07-24 12:25:46,989 INFO    MainThread:2071116 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-24 12:25:47,861 INFO    MainThread:2071116 [wandb_init.py:init():1029] starting run threads in backend
2025-07-24 12:25:49,258 INFO    MainThread:2071116 [wandb_run.py:_console_start():2458] atexit reg
2025-07-24 12:25:49,258 INFO    MainThread:2071116 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-24 12:25:49,258 INFO    MainThread:2071116 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-24 12:25:49,258 INFO    MainThread:2071116 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-24 12:25:49,273 INFO    MainThread:2071116 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-24 13:54:29,996 INFO    MainThread:2071116 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/rsbgmv10
2025-07-24 13:54:29,996 INFO    MainThread:2071116 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-24 13:54:29,997 INFO    MainThread:2071116 [wandb_run.py:_restore():2405] restore
2025-07-24 13:54:29,997 INFO    MainThread:2071116 [wandb_run.py:_restore():2411] restore done
2025-07-24 13:54:33,466 INFO    MainThread:2071116 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-24 13:54:33,468 INFO    MainThread:2071116 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-24 13:54:33,470 INFO    MainThread:2071116 [wandb_run.py:_footer_sync_info():3864] logging synced files
