_wandb:
    value:
        cli_version: 0.21.0
        e:
            2jixvgr5zlzodukhsrqw669nz7kal7dh:
                args:
                    - --selection_methods
                    - random
                    - moderate
                    - k_center
                    - diversity
                    - --num_samples
                    - "10"
                    - --num_epochs
                    - "3"
                    - --format_type
                    - chat
                codePath: run_dialogsum_experiment.py
                codePathLocal: run_dialogsum_experiment.py
                cpu_count: 6
                cpu_count_logical: 6
                cudaVersion: "12.9"
                disk:
                    /:
                        total: "982820896768"
                        used: "558426333184"
                email: <EMAIL>
                executable: /storage/nammt/data_selection_for_assistant_model/venv/bin/python
                git:
                    commit: bf1ea46b22cc79549df421fcd92c9cd2d3408008
                    remote: https://github.com/adymaharana/d2pruning.git
                gpu: NVIDIA RTX A4000
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6144
                      memoryTotal: "17171480576"
                      name: NVIDIA RTX A4000
                      uuid: GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a
                host: csews-Precision-7920-Tower
                memory:
                    total: "33291022336"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py
                python: CPython 3.10.12
                root: /storage/nammt/data_selection_for_assistant_model/d2pruning
                startedAt: "2025-07-24T12:57:41.740117Z"
                writerId: 2jixvgr5zlzodukhsrqw669nz7kal7dh
        m: []
        python_version: 3.10.12
        t:
            "1":
                - 1
            "2":
                - 1
            "3":
                - 13
                - 15
                - 16
            "4": 3.10.12
            "5": 0.21.0
            "10":
                - 20
            "12": 0.21.0
            "13": linux-x86_64
experiment_type:
    value: dialogsum_data_selection
format_type:
    value: chat
max_test_samples:
    value: 500
model_name:
    value: meta-llama/Llama-2-7b-hf
num_epochs:
    value: 3
num_samples:
    value: 10
selection_methods:
    value:
        - random
        - moderate
        - k_center
        - diversity
