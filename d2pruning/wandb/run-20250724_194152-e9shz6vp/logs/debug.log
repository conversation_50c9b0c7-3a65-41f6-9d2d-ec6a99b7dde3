2025-07-24 19:41:52,293 INFO    MainThread:2555981 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-24 19:41:52,293 INFO    MainThread:2555981 [wandb_setup.py:_flush():80] Configure stats pid to 2555981
2025-07-24 19:41:52,293 INFO    MainThread:2555981 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-24 19:41:52,293 INFO    MainThread:2555981 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-24 19:41:52,294 INFO    MainThread:2555981 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-24 19:41:52,294 INFO    MainThread:2555981 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_194152-e9shz6vp/logs/debug.log
2025-07-24 19:41:52,294 INFO    MainThread:2555981 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_194152-e9shz6vp/logs/debug-internal.log
2025-07-24 19:41:52,294 INFO    MainThread:2555981 [wandb_init.py:init():830] calling init triggers
2025-07-24 19:41:52,294 INFO    MainThread:2555981 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_methods': ['random', 'moderate', 'k_center', 'diversity'], 'num_samples': 10, 'model_name': 'meta-llama/Llama-2-7b-hf', 'format_type': 'chat', 'num_epochs': 3, 'max_test_samples': 500, 'experiment_type': 'dialogsum_data_selection', '_wandb': {}}
2025-07-24 19:41:52,294 INFO    MainThread:2555981 [wandb_init.py:init():871] starting backend
2025-07-24 19:41:52,510 INFO    MainThread:2555981 [wandb_init.py:init():874] sending inform_init request
2025-07-24 19:41:52,518 INFO    MainThread:2555981 [wandb_init.py:init():882] backend started and connected
2025-07-24 19:41:52,524 INFO    MainThread:2555981 [wandb_init.py:init():953] updated telemetry
2025-07-24 19:41:52,536 INFO    MainThread:2555981 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-24 19:41:53,513 INFO    MainThread:2555981 [wandb_init.py:init():1029] starting run threads in backend
2025-07-24 19:41:53,791 INFO    MainThread:2555981 [wandb_run.py:_console_start():2458] atexit reg
2025-07-24 19:41:53,792 INFO    MainThread:2555981 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-24 19:41:53,792 INFO    MainThread:2555981 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-24 19:41:53,792 INFO    MainThread:2555981 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-24 19:41:53,797 INFO    MainThread:2555981 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-24 19:45:30,087 INFO    MsgRouterThr:2555981 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
2025-07-24 19:45:30,705 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,732 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,733 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,734 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,735 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,736 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,743 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,744 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,745 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,746 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,747 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,748 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,749 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,750 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,751 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,752 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,753 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,754 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,756 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,756 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,757 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,758 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,759 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,760 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,761 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,761 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,763 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,763 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,764 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,765 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,765 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,766 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
2025-07-24 19:45:30,766 ERROR   MainThread:2555981 [redirect.py:_on_write():664] [all runs] error in stderr callback
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/redirect.py", line 662, in _on_write
    cb(written_data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 2385, in <lambda>
    lambda data: self._console_raw_callback("stderr", data),
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 398, in wrapper
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 456, in wrapper_fn
    return func(self, *args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_run.py", line 1566, in _console_raw_callback
    self._backend.interface.publish_output_raw(name, data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface.py", line 771, in publish_output_raw
    self._publish_output_raw(o)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_shared.py", line 38, in _publish_output_raw
    self._publish(rec)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/interface/interface_sock.py", line 39, in _publish
    self._sock_client.send_record_publish(record)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 170, in send_record_publish
    self.send_server_request(server_req)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 150, in send_server_request
    self._send_message(msg)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 147, in _send_message
    self._sendall_with_error_handle(header + data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/sock_client.py", line 126, in _sendall_with_error_handle
    sent = self._sock.send(data)
BrokenPipeError: [Errno 32] Broken pipe
