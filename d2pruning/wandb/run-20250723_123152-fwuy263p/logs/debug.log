2025-07-23 12:31:52,269 INFO    MainThread:494627 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-23 12:31:52,269 INFO    MainThread:494627 [wandb_setup.py:_flush():80] Configure stats pid to 494627
2025-07-23 12:31:52,269 INFO    MainThread:494627 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-23 12:31:52,269 INFO    MainThread:494627 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-23 12:31:52,269 INFO    MainThread:494627 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-23 12:31:52,269 INFO    MainThread:494627 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250723_123152-fwuy263p/logs/debug.log
2025-07-23 12:31:52,270 INFO    MainThread:494627 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250723_123152-fwuy263p/logs/debug-internal.log
2025-07-23 12:31:52,270 INFO    MainThread:494627 [wandb_init.py:init():830] calling init triggers
2025-07-23 12:31:52,270 INFO    MainThread:494627 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_methods': ['random', 'moderate', 'k_center'], 'num_samples': 10, 'model_name': 'meta-llama/Llama-2-7b-hf', 'format_type': 'instruction', 'num_epochs': 3, 'max_test_samples': 100, 'experiment_type': 'dialogsum_data_selection', '_wandb': {}}
2025-07-23 12:31:52,270 INFO    MainThread:494627 [wandb_init.py:init():871] starting backend
2025-07-23 12:31:52,895 INFO    MainThread:494627 [wandb_init.py:init():874] sending inform_init request
2025-07-23 12:31:52,903 INFO    MainThread:494627 [wandb_init.py:init():882] backend started and connected
2025-07-23 12:31:52,906 INFO    MainThread:494627 [wandb_init.py:init():953] updated telemetry
2025-07-23 12:31:52,922 INFO    MainThread:494627 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-23 12:31:54,044 INFO    MainThread:494627 [wandb_init.py:init():1029] starting run threads in backend
2025-07-23 12:31:54,318 INFO    MainThread:494627 [wandb_run.py:_console_start():2458] atexit reg
2025-07-23 12:31:54,318 INFO    MainThread:494627 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-23 12:31:54,319 INFO    MainThread:494627 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-23 12:31:54,319 INFO    MainThread:494627 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-23 12:31:54,323 INFO    MainThread:494627 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-23 12:34:11,599 INFO    MsgRouterThr:494627 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
