07/23/2025 12:43:25 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_instruction_20250723_124323
07/23/2025 12:43:25 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/crmc485x
07/23/2025 12:43:25 - INFO - __main__ - ============================================================
07/23/2025 12:43:25 - INFO - __main__ - STEP 1: DATA SELECTION
07/23/2025 12:43:25 - INFO - __main__ - ============================================================
07/23/2025 12:43:25 - INFO - __main__ - Running data selection with method: random
07/23/2025 12:43:25 - INFO - __main__ - Selection results already exist: results/selection_results_random_10.json
07/23/2025 12:43:25 - INFO - __main__ - Running data selection with method: moderate
07/23/2025 12:43:25 - INFO - __main__ - Selection results already exist: results/selection_results_moderate_10.json
07/23/2025 12:43:25 - INFO - __main__ - Running data selection with method: k_center
07/23/2025 12:43:25 - INFO - __main__ - Selection results already exist: results/selection_results_k_center_10.json
07/23/2025 12:43:25 - INFO - __main__ - ============================================================
07/23/2025 12:43:25 - INFO - __main__ - STEP 2: FINE-TUNING
07/23/2025 12:43:25 - INFO - __main__ - ============================================================
07/23/2025 12:43:25 - INFO - __main__ - Running fine-tuning for results/selection_results_random_10.json
07/23/2025 12:43:25 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_random_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_random_10
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 400, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 382, in main
    experiment_summary = experiment.run_complete_experiment(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 222, in run_complete_experiment
    model_dir = self.run_finetuning(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 124, in run_finetuning
    result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
  File "/usr/lib/python3.10/subprocess.py", line 505, in run
    stdout, stderr = process.communicate(input, timeout=timeout)
  File "/usr/lib/python3.10/subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "/usr/lib/python3.10/subprocess.py", line 2021, in _communicate
    ready = selector.select(timeout)
  File "/usr/lib/python3.10/selectors.py", line 416, in select
    fd_event_list = self._selector.poll(timeout)
KeyboardInterrupt
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 400, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 382, in main
    experiment_summary = experiment.run_complete_experiment(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 222, in run_complete_experiment
    model_dir = self.run_finetuning(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", line 124, in run_finetuning
    result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
  File "/usr/lib/python3.10/subprocess.py", line 505, in run
    stdout, stderr = process.communicate(input, timeout=timeout)
  File "/usr/lib/python3.10/subprocess.py", line 1154, in communicate
    stdout, stderr = self._communicate(input, endtime, timeout)
  File "/usr/lib/python3.10/subprocess.py", line 2021, in _communicate
    ready = selector.select(timeout)
  File "/usr/lib/python3.10/selectors.py", line 416, in select
    fd_event_list = self._selector.poll(timeout)
KeyboardInterrupt
