07/25/2025 06:50:06 - INFO - __main__ - Loading model from d2pruning/finetuned_models/random_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 06:50:06 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:24<00:00, 12.01s/it]
07/25/2025 06:50:33 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 06:50:33 - INFO - __main__ - Starting summary generation...
Generating Summaries:   0%|          | 0/13 [00:00<?, ?it/s]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
Generating Summaries:   8%|▊         | 1/13 [00:38<07:45, 38.77s/it]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
Generating Summaries:   8%|▊         | 1/13 [01:18<15:41, 78.49s/it]
07/25/2025 06:51:51 - ERROR - __main__ - Failed to evaluate model random_10samples_chat: list index out of range
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 291, in main
    predictions, references = evaluator.generate_summaries(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 157, in generate_summaries
    cleaned_preds = [pred.split("Summary:")[1].strip() for pred in decoded_preds]
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 157, in <listcomp>
    cleaned_preds = [pred.split("Summary:")[1].strip() for pred in decoded_preds]
IndexError: list index out of range
