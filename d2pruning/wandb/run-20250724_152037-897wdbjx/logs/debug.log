2025-07-24 15:20:37,874 INFO    MainThread:2266302 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-24 15:20:37,875 INFO    MainThread:2266302 [wandb_setup.py:_flush():80] Configure stats pid to 2266302
2025-07-24 15:20:37,875 INFO    MainThread:2266302 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-24 15:20:37,875 INFO    MainThread:2266302 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-24 15:20:37,875 INFO    MainThread:2266302 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-24 15:20:37,875 INFO    MainThread:2266302 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_152037-897wdbjx/logs/debug.log
2025-07-24 15:20:37,875 INFO    MainThread:2266302 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_152037-897wdbjx/logs/debug-internal.log
2025-07-24 15:20:37,876 INFO    MainThread:2266302 [wandb_init.py:init():830] calling init triggers
2025-07-24 15:20:37,876 INFO    MainThread:2266302 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_methods': ['random', 'moderate', 'k_center'], 'num_samples': 10, 'model_name': 'meta-llama/Llama-2-7b-hf', 'format_type': 'alpaca', 'num_epochs': 3, 'max_test_samples': 100, 'experiment_type': 'dialogsum_data_selection', '_wandb': {}}
2025-07-24 15:20:37,876 INFO    MainThread:2266302 [wandb_init.py:init():871] starting backend
2025-07-24 15:20:38,091 INFO    MainThread:2266302 [wandb_init.py:init():874] sending inform_init request
2025-07-24 15:20:38,100 INFO    MainThread:2266302 [wandb_init.py:init():882] backend started and connected
2025-07-24 15:20:38,102 INFO    MainThread:2266302 [wandb_init.py:init():953] updated telemetry
2025-07-24 15:20:38,117 INFO    MainThread:2266302 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-24 15:20:39,008 INFO    MainThread:2266302 [wandb_init.py:init():1029] starting run threads in backend
2025-07-24 15:20:39,280 INFO    MainThread:2266302 [wandb_run.py:_console_start():2458] atexit reg
2025-07-24 15:20:39,281 INFO    MainThread:2266302 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-24 15:20:39,281 INFO    MainThread:2266302 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-24 15:20:39,281 INFO    MainThread:2266302 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-24 15:20:39,286 INFO    MainThread:2266302 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-24 16:45:37,064 INFO    MainThread:2266302 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/897wdbjx
2025-07-24 16:45:37,077 INFO    MainThread:2266302 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-24 16:45:37,079 INFO    MainThread:2266302 [wandb_run.py:_restore():2405] restore
2025-07-24 16:45:37,081 INFO    MainThread:2266302 [wandb_run.py:_restore():2411] restore done
2025-07-24 16:45:40,636 INFO    MainThread:2266302 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-24 16:45:40,639 INFO    MainThread:2266302 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-24 16:45:40,642 INFO    MainThread:2266302 [wandb_run.py:_footer_sync_info():3864] logging synced files
