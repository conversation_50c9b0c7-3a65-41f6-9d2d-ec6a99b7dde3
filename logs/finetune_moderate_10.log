07/25/2025 07:27:26 - INFO - __main__ - Logging to ../logs/finetune_20250725_072726.log
07/25/2025 07:27:26 - INFO - __main__ - Loading selection results from d2pruning/results/selection_results_moderate_10.json
wandb: Currently logged in as: joshua<PERSON>shu<PERSON> (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: WARNING Using a boolean value for 'reinit' is deprecated. Use 'return_previous' or 'finish_previous' instead.
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_072727-b8x6bdp6
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_moderate_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/b8x6bdp6
07/25/2025 07:27:30 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/25/2025 07:27:30 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/b8x6bdp6
07/25/2025 07:27:30 - INFO - __main__ - W&B logging initialized with run name: finetune_moderate_10
07/25/2025 07:27:30 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 07:27:33 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:43<00:43, 43.59s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:57<00:00, 26.32s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:57<00:00, 28.91s/it]
07/25/2025 07:28:32 - INFO - __main__ - Preparing training dataset...
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 166.57 examples/s]
07/25/2025 07:28:55 - INFO - __main__ - Starting fine-tuning...
07/25/2025 07:28:55 - INFO - wandb_logger - Logged training start for moderate to W&B
Using auto half precision backend
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
***** Running training *****
  Num examples = 10
  Num Epochs = 3
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 4
  Gradient Accumulation steps = 4
  Total optimization steps = 9
  Number of trainable parameters = 16,777,216
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/9 [00:00<?, ?it/s]
 11%|█         | 1/9 [00:02<00:23,  2.92s/it]
                                             

 11%|█         | 1/9 [00:02<00:23,  2.92s/it]
 22%|██▏       | 2/9 [00:04<00:16,  2.36s/it]
                                             

 22%|██▏       | 2/9 [00:04<00:16,  2.36s/it]
 33%|███▎      | 3/9 [00:05<00:10,  1.72s/it]
                                             

 33%|███▎      | 3/9 [00:05<00:10,  1.72s/it]Saving model checkpoint to d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-3
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-3/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-3/special_tokens_map.json

 44%|████▍     | 4/9 [00:10<00:15,  3.00s/it]
                                             

 44%|████▍     | 4/9 [00:10<00:15,  3.00s/it]
 56%|█████▌    | 5/9 [00:12<00:10,  2.58s/it]
                                             

 56%|█████▌    | 5/9 [00:12<00:10,  2.58s/it]
 67%|██████▋   | 6/9 [00:13<00:06,  2.01s/it]
                                             

 67%|██████▋   | 6/9 [00:13<00:06,  2.01s/it]Saving model checkpoint to d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-6
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-6/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-6/special_tokens_map.json

 78%|███████▊  | 7/9 [00:18<00:05,  2.96s/it]
                                             

 78%|███████▊  | 7/9 [00:18<00:05,  2.96s/it]
 89%|████████▉ | 8/9 [00:20<00:02,  2.60s/it]
                                             

 89%|████████▉ | 8/9 [00:20<00:02,  2.60s/it]
100%|██████████| 9/9 [00:21<00:00,  2.07s/it]
                                             

100%|██████████| 9/9 [00:21<00:00,  2.07s/it]Saving model checkpoint to d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-9
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-9/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/moderate_10samples_chat/checkpoint-9/special_tokens_map.json


Training completed. Do not forget to share your model on huggingface.co/models =)



                                             

100%|██████████| 9/9 [00:24<00:00,  2.07s/it]
100%|██████████| 9/9 [00:24<00:00,  2.70s/it]
07/25/2025 07:29:20 - INFO - wandb_logger - Logged training completion for moderate to W&B
Saving model checkpoint to d2pruning/finetuned_models/moderate_10samples_chat
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/moderate_10samples_chat/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/moderate_10samples_chat/special_tokens_map.json
07/25/2025 07:29:22 - INFO - __main__ - Fine-tuning completed in 24.63 seconds
wandb: uploading artifact run-b8x6bdp6-trainingmoderatesummary_table-d756bf179e712cb67609892b4e6550f2; uploading output.log; uploading config.yaml
wandb: uploading artifact run-b8x6bdp6-trainingmoderatesummary_table-d756bf179e712cb67609892b4e6550f2
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:              experiment/total_duration ▁
wandb:                            train/epoch ▁▂▃▄▅▅▆▇██
wandb:                      train/global_step ▁▂▂▃▃▃▃▄▄▅▅▆▆▆▆▇▇████████
wandb:                        train/grad_norm ▅▃▁▃▁█▆▂▅
wandb:                    train/learning_rate ▁▂▃▄▅▅▆▇█
wandb:                             train/loss ▆▆▄█▅▁▄█▂
wandb:           training/moderate/batch_size ▁
wandb:                training/moderate/epoch ▁▁
wandb:           training/moderate/final_loss ▁
wandb:            training/moderate/grad_norm ▅▃▁▃▁█▆▂▅
wandb:        training/moderate/learning_rate █▁▁▁▁▁▁▁▁▂▁
wandb:                 training/moderate/loss ▁▁
wandb:          training/moderate/loss_smooth ▁
wandb:           training/moderate/num_epochs ▁▁
wandb:          training/moderate/num_samples ▁
wandb:              training/moderate/runtime ▁
wandb:   training/moderate/samples_per_second ▁
wandb:           training/moderate/start_time ▁
wandb:                 training/moderate/step ▁▁
wandb:       training/moderate/time_per_epoch ▁
wandb:     training/moderate/total_parameters ▁
wandb:          training/moderate/total_steps ▁
wandb:           training/moderate/total_time ▁
wandb: training/moderate/trainable_parameters ▁
wandb:      training/moderate/trainable_ratio ▁
wandb: 
wandb: Run summary:
wandb:              experiment/total_duration 115.54021
wandb:                             total_flos 344348297330688.0
wandb:                            train/epoch 3
wandb:                      train/global_step 9
wandb:                        train/grad_norm 0.49921
wandb:                    train/learning_rate 2e-05
wandb:                             train/loss 1.9432
wandb:                             train_loss 2.01906
wandb:                          train_runtime 24.2905
wandb:               train_samples_per_second 1.235
wandb:                 train_steps_per_second 0.371
wandb:           training/moderate/batch_size 1
wandb:                training/moderate/epoch 3
wandb:           training/moderate/final_loss 2.01906
wandb:          training/moderate/format_type instruction
wandb:            training/moderate/grad_norm 0.49921
wandb:        training/moderate/learning_rate 0
wandb:                 training/moderate/loss 2.01906
wandb:          training/moderate/loss_smooth 2.01906
wandb:           training/moderate/model_name meta-llama/Llama-2-7...
wandb:           training/moderate/num_epochs 3
wandb:          training/moderate/num_samples 10
wandb:              training/moderate/runtime 24.2905
wandb:   training/moderate/samples_per_second 1.235
wandb:           training/moderate/start_time 1753403335.18007
wandb:               training/moderate/status completed
wandb:                 training/moderate/step 9
wandb:       training/moderate/time_per_epoch 8.21054
wandb:     training/moderate/total_parameters 3517190144
wandb:          training/moderate/total_steps 9
wandb:           training/moderate/total_time 24.63161
wandb: training/moderate/trainable_parameters 16777216
wandb:      training/moderate/trainable_ratio 0.00477
wandb: 
wandb: 🚀 View run finetune_moderate_10 at: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/b8x6bdp6
wandb: ⭐️ View project at: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark
wandb: Synced 5 W&B file(s), 1 media file(s), 2 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250725_072727-b8x6bdp6/logs
07/25/2025 07:29:25 - INFO - wandb_logger - Finished W&B run
07/25/2025 07:29:25 - INFO - __main__ - Training results saved to d2pruning/finetuned_models/moderate_10samples_chat/training_results.json
07/25/2025 07:29:25 - INFO - __main__ - Fine-tuning completed successfully!
07/25/2025 07:29:25 - INFO - __main__ - Fine-tuning complete.
{'loss': 2.051, 'grad_norm': 0.5073545575141907, 'learning_rate': 0.0, 'epoch': 0.4}
{'loss': 2.052, 'grad_norm': 0.47231969237327576, 'learning_rate': 2.0000000000000003e-06, 'epoch': 0.8}
{'loss': 1.9976, 'grad_norm': 0.4352986514568329, 'learning_rate': 4.000000000000001e-06, 'epoch': 1.0}
{'loss': 2.1044, 'grad_norm': 0.46920743584632874, 'learning_rate': 6e-06, 'epoch': 1.4}
{'loss': 2.0205, 'grad_norm': 0.43427613377571106, 'learning_rate': 8.000000000000001e-06, 'epoch': 1.8}
{'loss': 1.9178, 'grad_norm': 0.5604689717292786, 'learning_rate': 1e-05, 'epoch': 2.0}
{'loss': 1.9928, 'grad_norm': 0.5168476104736328, 'learning_rate': 1.2e-05, 'epoch': 2.4}
{'loss': 2.0923, 'grad_norm': 0.4527691602706909, 'learning_rate': 1.4000000000000001e-05, 'epoch': 2.8}
{'loss': 1.9432, 'grad_norm': 0.4992084801197052, 'learning_rate': 1.6000000000000003e-05, 'epoch': 3.0}
{'train_runtime': 24.2905, 'train_samples_per_second': 1.235, 'train_steps_per_second': 0.371, 'train_loss': 2.019058929549323, 'epoch': 3.0}
