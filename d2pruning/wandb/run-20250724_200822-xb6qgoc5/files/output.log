07/24/2025 20:08:23 - INFO - wandb_logger - Initialized W&B run: selection_random_10
07/24/2025 20:08:23 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/xb6qgoc5
07/24/2025 20:08:23 - INFO - __main__ - Starting DialogSum data selection pipeline
07/24/2025 20:08:23 - INFO - __main__ - Selection method: random
07/24/2025 20:08:23 - INFO - __main__ - Number of samples: 10
07/24/2025 20:08:23 - INFO - __main__ - Embedding model: meta-llama/Llama-2-7b-hf
07/24/2025 20:08:23 - INFO - __main__ - Loading DialogSum dataset...
07/24/2025 20:08:23 - INFO - __main__ - Loading cached DialogSum train dataset from cache/dialogsum_train.pkl
07/24/2025 20:08:23 - INFO - __main__ - Loading cached DialogSum test dataset from cache/dialogsum_test.pkl
07/24/2025 20:08:23 - INFO - __main__ - Train dataset size: 12460
07/24/2025 20:08:23 - INFO - __main__ - Test dataset size: 1500
07/24/2025 20:08:24 - INFO - __main__ - Preprocessed 12460 training samples
07/24/2025 20:08:24 - INFO - __main__ - Average dialogue length: 131.0 words
07/24/2025 20:08:24 - INFO - __main__ - Average summary length: 22.9 words
07/24/2025 20:08:24 - INFO - wandb_logger - Logged dataset information to W&B
07/24/2025 20:08:24 - INFO - __main__ - Extracting embeddings...
07/24/2025 20:08:24 - INFO - __main__ - Loading LLaMA model for embeddings: meta-llama/Llama-2-7b-hf
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
Fetching 2 files:   0%|          | 0/2 [2:18:48<?, ?it/s]
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/dialogsum_selection.py", line 460, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/dialogsum_selection.py", line 365, in main
    embeddings = extractor.extract_embeddings(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/dialogsum_selection.py", line 144, in extract_embeddings
    self._load_model()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/dialogsum_selection.py", line 123, in _load_model
    self.model = AutoModel.from_pretrained(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/models/auto/auto_factory.py", line 600, in from_pretrained
    return model_class.from_pretrained(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 311, in _wrapper
    return func(*args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 4680, in from_pretrained
    checkpoint_files, sharded_metadata = _get_resolved_checkpoint_files(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 1295, in _get_resolved_checkpoint_files
    checkpoint_files, sharded_metadata = get_checkpoint_shard_files(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/utils/hub.py", line 1110, in get_checkpoint_shard_files
    cached_filenames = cached_files(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/utils/hub.py", line 485, in cached_files
    snapshot_download(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/huggingface_hub/_snapshot_download.py", line 327, in snapshot_download
    thread_map(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/tqdm/contrib/concurrent.py", line 69, in thread_map
    return _executor_map(ThreadPoolExecutor, fn, *iterables, **tqdm_kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/tqdm/contrib/concurrent.py", line 51, in _executor_map
    return list(tqdm_class(ex.map(fn, *iterables, chunksize=chunksize), **kwargs))
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 621, in result_iterator
    yield _result_or_cancel(fs.pop())
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 319, in _result_or_cancel
    return fut.result(timeout)
  File "/usr/lib/python3.10/concurrent/futures/_base.py", line 453, in result
    self._condition.wait(timeout)
  File "/usr/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
KeyboardInterrupt
