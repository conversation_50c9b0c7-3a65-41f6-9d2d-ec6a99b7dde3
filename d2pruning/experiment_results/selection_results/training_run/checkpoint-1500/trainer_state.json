{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 0.962772785622593, "eval_steps": 500, "global_step": 1500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.03209242618741977, "grad_norm": Infinity, "learning_rate": 4.849165596919127e-05, "loss": 0.5574, "step": 50}, {"epoch": 0.06418485237483953, "grad_norm": 3.575394630432129, "learning_rate": 4.69191270860077e-05, "loss": 0.7187, "step": 100}, {"epoch": 0.0962772785622593, "grad_norm": 8.070857048034668, "learning_rate": 4.531450577663672e-05, "loss": 0.7305, "step": 150}, {"epoch": 0.12836970474967907, "grad_norm": 3.668419361114502, "learning_rate": 4.370988446726573e-05, "loss": 0.56, "step": 200}, {"epoch": 0.16046213093709885, "grad_norm": 16.039926528930664, "learning_rate": 4.210526315789474e-05, "loss": 0.5477, "step": 250}, {"epoch": 0.1925545571245186, "grad_norm": 6.599456787109375, "learning_rate": 4.0500641848523754e-05, "loss": 0.5126, "step": 300}, {"epoch": 0.2246469833119384, "grad_norm": 4.171901226043701, "learning_rate": 3.8896020539152764e-05, "loss": 0.4713, "step": 350}, {"epoch": 0.25673940949935814, "grad_norm": 3.7021212577819824, "learning_rate": 3.7291399229781774e-05, "loss": 0.4231, "step": 400}, {"epoch": 0.2888318356867779, "grad_norm": 11.612005233764648, "learning_rate": 3.5686777920410784e-05, "loss": 0.4681, "step": 450}, {"epoch": 0.3209242618741977, "grad_norm": 0.7501663565635681, "learning_rate": 3.4082156611039793e-05, "loss": 0.4302, "step": 500}, {"epoch": 0.35301668806161746, "grad_norm": 3.411372184753418, "learning_rate": 3.247753530166881e-05, "loss": 0.4545, "step": 550}, {"epoch": 0.3851091142490372, "grad_norm": 8.383039474487305, "learning_rate": 3.087291399229782e-05, "loss": 0.3783, "step": 600}, {"epoch": 0.417201540436457, "grad_norm": 5.486710071563721, "learning_rate": 2.930038510911425e-05, "loss": 0.3754, "step": 650}, {"epoch": 0.4492939666238768, "grad_norm": 16.48038673400879, "learning_rate": 2.7695763799743263e-05, "loss": 0.406, "step": 700}, {"epoch": 0.4813863928112965, "grad_norm": 215.326171875, "learning_rate": 2.6091142490372272e-05, "loss": 0.4424, "step": 750}, {"epoch": 0.5134788189987163, "grad_norm": 3.0576088428497314, "learning_rate": 2.4486521181001286e-05, "loss": 0.3559, "step": 800}, {"epoch": 0.5455712451861361, "grad_norm": 18.776691436767578, "learning_rate": 2.2881899871630295e-05, "loss": 0.4693, "step": 850}, {"epoch": 0.5776636713735558, "grad_norm": 1.892021894454956, "learning_rate": 2.127727856225931e-05, "loss": 0.3974, "step": 900}, {"epoch": 0.6097560975609756, "grad_norm": 95.29884338378906, "learning_rate": 1.9672657252888318e-05, "loss": 0.4095, "step": 950}, {"epoch": 0.6418485237483954, "grad_norm": 6.439931869506836, "learning_rate": 1.806803594351733e-05, "loss": 0.425, "step": 1000}, {"epoch": 0.6739409499358151, "grad_norm": 48.00126647949219, "learning_rate": 1.6463414634146345e-05, "loss": 0.4008, "step": 1050}, {"epoch": 0.7060333761232349, "grad_norm": 5.977982997894287, "learning_rate": 1.4858793324775353e-05, "loss": 0.4451, "step": 1100}, {"epoch": 0.7381258023106547, "grad_norm": 5.003472328186035, "learning_rate": 1.3254172015404364e-05, "loss": 0.3917, "step": 1150}, {"epoch": 0.7702182284980744, "grad_norm": 5.390883445739746, "learning_rate": 1.1649550706033377e-05, "loss": 0.4765, "step": 1200}, {"epoch": 0.8023106546854942, "grad_norm": 1.85026216506958, "learning_rate": 1.0044929396662387e-05, "loss": 0.3865, "step": 1250}, {"epoch": 0.834403080872914, "grad_norm": 3.4627685546875, "learning_rate": 8.4403080872914e-06, "loss": 0.3297, "step": 1300}, {"epoch": 0.8664955070603337, "grad_norm": 3.704845905303955, "learning_rate": 6.835686777920411e-06, "loss": 0.3411, "step": 1350}, {"epoch": 0.8985879332477535, "grad_norm": 5.375056743621826, "learning_rate": 5.231065468549422e-06, "loss": 0.4522, "step": 1400}, {"epoch": 0.9306803594351734, "grad_norm": 8.738109588623047, "learning_rate": 3.626444159178434e-06, "loss": 0.4257, "step": 1450}, {"epoch": 0.962772785622593, "grad_norm": 69.17483520507812, "learning_rate": 2.0218228498074456e-06, "loss": 0.3994, "step": 1500}], "logging_steps": 50, "max_steps": 1558, "num_input_tokens_seen": 0, "num_train_epochs": 1, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2206029977004480.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}