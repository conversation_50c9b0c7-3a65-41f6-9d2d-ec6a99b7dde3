07/24/2025 08:47:09 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 08:47:09 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/5xsea3kz
07/24/2025 08:47:09 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 419, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 380, in main
    finetuner.load_model_and_tokenizer()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 139, in load_model_and_tokenizer
    self.model = AutoModelForCausalLM.from_pretrained(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/models/auto/auto_factory.py", line 600, in from_pretrained
    return model_class.from_pretrained(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 311, in _wrapper
    return func(*args, **kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 4680, in from_pretrained
    checkpoint_files, sharded_metadata = _get_resolved_checkpoint_files(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/modeling_utils.py", line 1295, in _get_resolved_checkpoint_files
    checkpoint_files, sharded_metadata = get_checkpoint_shard_files(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/utils/hub.py", line 1110, in get_checkpoint_shard_files
    cached_filenames = cached_files(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/utils/hub.py", line 573, in cached_files
    raise OSError(
OSError: meta-llama/Llama-2-7b-hf does not appear to have a file named model-00001-of-00002.safetensors. Checkout 'https://huggingface.co/meta-llama/Llama-2-7b-hf/tree/main' for available files.
