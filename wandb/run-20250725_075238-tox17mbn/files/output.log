07/25/2025 07:52:39 - INFO - __main__ - Loading model from d2pruning/finetuned_models/moderate_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 07:52:39 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:23<00:00, 11.91s/it]
07/25/2025 07:53:06 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 07:53:06 - INFO - __main__ - Starting summary generation...
Generating Summaries: 100%|██████████| 13/13 [08:16<00:00, 38.21s/it]
07/25/2025 08:01:23 - INFO - __main__ - Generated 50 summaries.
07/25/2025 08:01:23 - INFO - __main__ - Computing and logging metrics...
Calculating ROUGE:   0%|          | 0/5 [00:00<?, ?it/s]07/25/2025 08:01:23 - INFO - absl - Using default tokenizer.
07/25/2025 08:01:23 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  20%|██        | 1/5 [00:00<00:02,  1.57it/s]07/25/2025 08:01:24 - INFO - absl - Using default tokenizer.
07/25/2025 08:01:24 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  40%|████      | 2/5 [00:01<00:02,  1.47it/s]07/25/2025 08:01:24 - INFO - absl - Using default tokenizer.
07/25/2025 08:01:25 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  60%|██████    | 3/5 [00:02<00:01,  1.40it/s]07/25/2025 08:01:25 - INFO - absl - Using default tokenizer.
07/25/2025 08:01:26 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  80%|████████  | 4/5 [00:02<00:00,  1.31it/s]07/25/2025 08:01:26 - INFO - absl - Using default tokenizer.
07/25/2025 08:01:26 - INFO - absl - Using default tokenizer.
Calculating ROUGE: 100%|██████████| 5/5 [00:03<00:00,  1.28it/s]
07/25/2025 08:01:27 - INFO - absl - Using default tokenizer.
07/25/2025 08:01:28 - INFO - __main__ - Final ROUGE scores: {'rouge1': np.float64(0.18749356824807983), 'rouge2': np.float64(0.03383629545403782), 'rougeL': np.float64(0.13343279483099973), 'rougeLsum': np.float64(0.15525872894685516)}
