07/25/2025 07:32:19 - INFO - wandb_logger - Initialized W&B run: finetune_diversity_10
07/25/2025 07:32:19 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/1vbgk50j
07/25/2025 07:32:19 - INFO - __main__ - W&B logging initialized with run name: finetune_diversity_10
07/25/2025 07:32:19 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 07:32:21 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:59<00:00, 29.73s/it]
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484
07/25/2025 07:33:23 - INFO - __main__ - Preparing training dataset...
Map: 100%|██████████| 10/10 [00:00<00:00, 404.90 examples/s]
07/25/2025 07:33:45 - INFO - __main__ - Starting fine-tuning...
07/25/2025 07:33:45 - INFO - wandb_logger - Logged training start for diversity to W&B
Using auto half precision backend
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
***** Running training *****
  Num examples = 10
  Num Epochs = 3
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 4
  Gradient Accumulation steps = 4
  Total optimization steps = 9
  Number of trainable parameters = 16,777,216
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"
 33%|███▎      | 3/9 [00:06<00:11,  1.84s/it]Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-3
{'loss': 1.9506, 'grad_norm': 0.29518041014671326, 'learning_rate': 0.0, 'epoch': 0.4}
{'loss': 2.2094, 'grad_norm': 0.48138654232025146, 'learning_rate': 2.0000000000000003e-06, 'epoch': 0.8}
{'loss': 1.9392, 'grad_norm': 0.32971176505088806, 'learning_rate': 4.000000000000001e-06, 'epoch': 1.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-3/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-3/special_tokens_map.json
 67%|██████▋   | 6/9 [00:14<00:06,  2.25s/it]Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-6
{'loss': 1.9839, 'grad_norm': 0.39397770166397095, 'learning_rate': 6e-06, 'epoch': 1.4}
{'loss': 2.0122, 'grad_norm': 0.35957279801368713, 'learning_rate': 8.000000000000001e-06, 'epoch': 1.8}
{'loss': 2.0725, 'grad_norm': 0.3390657603740692, 'learning_rate': 1e-05, 'epoch': 2.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-6/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-6/special_tokens_map.json
100%|██████████| 9/9 [00:23<00:00,  2.29s/it]Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-9
{'loss': 1.9451, 'grad_norm': 0.3051750659942627, 'learning_rate': 1.2e-05, 'epoch': 2.4}
{'loss': 1.935, 'grad_norm': 0.36684536933898926, 'learning_rate': 1.4000000000000001e-05, 'epoch': 2.8}
{'loss': 2.4665, 'grad_norm': 0.5520522594451904, 'learning_rate': 1.6000000000000003e-05, 'epoch': 3.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-9/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/checkpoint-9/special_tokens_map.json


Training completed. Do not forget to share your model on huggingface.co/models =)


100%|██████████| 9/9 [00:26<00:00,  2.92s/it]
{'train_runtime': 26.3052, 'train_samples_per_second': 1.14, 'train_steps_per_second': 0.342, 'train_loss': 2.0571490128835044, 'epoch': 3.0}
07/25/2025 07:34:13 - INFO - wandb_logger - Logged training completion for diversity to W&B
Saving model checkpoint to d2pruning/finetuned_models/diversity_10samples_chat
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/diversity_10samples_chat/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/diversity_10samples_chat/special_tokens_map.json
07/25/2025 07:34:14 - INFO - __main__ - Fine-tuning completed in 26.66 seconds
