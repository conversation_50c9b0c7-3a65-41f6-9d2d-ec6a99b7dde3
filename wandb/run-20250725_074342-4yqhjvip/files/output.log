07/25/2025 07:43:45 - INFO - __main__ - Loading model from d2pruning/finetuned_models/k_center_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 07:43:46 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:23<00:00, 11.94s/it]
07/25/2025 07:44:13 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 07:44:13 - INFO - __main__ - Starting summary generation...
Generating Summaries: 100%|██████████| 13/13 [08:16<00:00, 38.19s/it]
07/25/2025 07:52:29 - INFO - __main__ - Generated 50 summaries.
07/25/2025 07:52:29 - INFO - __main__ - Computing and logging metrics...
Calculating ROUGE:   0%|          | 0/5 [00:00<?, ?it/s]07/25/2025 07:52:29 - INFO - absl - Using default tokenizer.
07/25/2025 07:52:30 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  20%|██        | 1/5 [00:00<00:02,  1.60it/s]07/25/2025 07:52:30 - INFO - absl - Using default tokenizer.
07/25/2025 07:52:30 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  40%|████      | 2/5 [00:01<00:02,  1.49it/s]07/25/2025 07:52:31 - INFO - absl - Using default tokenizer.
07/25/2025 07:52:31 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  60%|██████    | 3/5 [00:02<00:01,  1.42it/s]07/25/2025 07:52:31 - INFO - absl - Using default tokenizer.
07/25/2025 07:52:32 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  80%|████████  | 4/5 [00:02<00:00,  1.33it/s]07/25/2025 07:52:32 - INFO - absl - Using default tokenizer.
07/25/2025 07:52:33 - INFO - absl - Using default tokenizer.
Calculating ROUGE: 100%|██████████| 5/5 [00:03<00:00,  1.29it/s]
07/25/2025 07:52:33 - INFO - absl - Using default tokenizer.
07/25/2025 07:52:34 - INFO - __main__ - Final ROUGE scores: {'rouge1': np.float64(0.1851121743546899), 'rouge2': np.float64(0.03300204664874168), 'rougeL': np.float64(0.13350049642073197), 'rougeLsum': np.float64(0.15088880680435388)}
