#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to prepare selection data for finetuning.
Takes a selection results file with indices and adds the corresponding dialogues and summaries.
"""

import os
import sys
import json
import pickle
import numpy as np
from pathlib import Path
from datasets import load_dataset

# Load the selection results
selection_file = Path("./results/selection_results_d2pruning_10.json")
output_file = selection_file  # Overwrite the original file

# Load the DialogSum train dataset
cache_dir = Path("./cache")
cache_file = cache_dir / "dialogsum_train.pkl"

if cache_file.exists():
    print(f"Loading cached DialogSum train dataset from {cache_file}")
    with open(cache_file, 'rb') as f:
        train_dataset = pickle.load(f)
else:
    print(f"Loading DialogSum train dataset from HuggingFace")
    train_dataset = load_dataset("knkarthick/dialogsum", split="train", cache_dir=cache_dir)
    
    # Cache the dataset for future use
    with open(cache_file, 'wb') as f:
        pickle.dump(train_dataset, f)

# Load the selection results
with open(selection_file, 'r') as f:
    selection_data = json.load(f)

# Get the selected indices
selected_indices = selection_data['selected_indices']
print(f"Selected indices: {selected_indices}")

# Extract dialogues and summaries for selected indices
selected_dialogues = []
selected_summaries = []

for idx in selected_indices:
    try:
        dialogue = train_dataset[idx]['dialogue']
        summary = train_dataset[idx]['summary']
        selected_dialogues.append(dialogue)
        selected_summaries.append(summary)
        print(f"Added sample {idx}")
    except Exception as e:
        print(f"Error processing index {idx}: {e}")

# Update the selection data
selection_data['selected_dialogues'] = selected_dialogues
selection_data['selected_summaries'] = selected_summaries

# Save the updated selection data
with open(output_file, 'w') as f:
    json.dump(selection_data, f, indent=2)

print(f"Updated selection results saved to {output_file}")
print(f"Added {len(selected_dialogues)} dialogues and {len(selected_summaries)} summaries")
