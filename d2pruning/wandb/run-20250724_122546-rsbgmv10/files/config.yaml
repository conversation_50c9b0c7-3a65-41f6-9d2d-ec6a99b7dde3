_wandb:
    value:
        cli_version: 0.21.0
        e:
            9ozkut6kgnmsix6i1aakvdj3ai998ss9:
                args:
                    - --model_dirs
                    - finetuned_models/random_10samples_instruction
                    - finetuned_models/moderate_10samples_instruction
                    - finetuned_models/k_center_10samples_instruction
                    - --base_model
                    - meta-llama/Llama-2-7b-hf
                    - --max_test_samples
                    - "100"
                    - --output_dir
                    - evaluation_results
                    - --cache_dir
                    - cache
                    - --enable_wandb
                    - --wandb_run_name
                    - eval_3models
                codePath: evaluate_summaries.py
                codePathLocal: evaluate_summaries.py
                cpu_count: 6
                cpu_count_logical: 6
                cudaVersion: "12.9"
                disk:
                    /:
                        total: "982820896768"
                        used: "558190735360"
                email: <EMAIL>
                executable: /storage/nammt/data_selection_for_assistant_model/venv/bin/python
                git:
                    commit: bf1ea46b22cc79549df421fcd92c9cd2d3408008
                    remote: https://github.com/adymaharana/d2pruning.git
                gpu: NVIDIA RTX A4000
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6144
                      memoryTotal: "17171480576"
                      name: NVIDIA RTX A4000
                      uuid: GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a
                host: csews-Precision-7920-Tower
                memory:
                    total: "33291022336"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py
                python: CPython 3.10.12
                root: /storage/nammt/data_selection_for_assistant_model/d2pruning
                startedAt: "2025-07-24T05:25:46.894549Z"
                writerId: 9ozkut6kgnmsix6i1aakvdj3ai998ss9
        m: []
        python_version: 3.10.12
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
                - 100
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
                - 100
            "3":
                - 2
                - 13
                - 15
                - 16
            "4": 3.10.12
            "5": 0.21.0
            "6": 4.53.2
            "12": 0.21.0
            "13": linux-x86_64
base_model:
    value: meta-llama/Llama-2-7b-hf
max_test_samples:
    value: 100
model_dirs:
    value:
        - finetuned_models/random_10samples_instruction
        - finetuned_models/moderate_10samples_instruction
        - finetuned_models/k_center_10samples_instruction
task:
    value: evaluation
