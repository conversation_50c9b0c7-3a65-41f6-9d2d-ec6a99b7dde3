07/25/2025 09:52:29 - INFO - __main__ - Logging to ../logs/finetune_20250725_095229.log
07/25/2025 09:52:29 - INFO - __main__ - Loading selection results from ./results/selection_results_d2pruning_10.json
07/25/2025 09:52:29 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 09:52:41 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
07/25/2025 09:54:16 - INFO - __main__ - Preparing training dataset...
07/25/2025 09:54:39 - INFO - __main__ - Starting fine-tuning...
07/25/2025 09:55:13 - INFO - __main__ - Fine-tuning completed in 32.95 seconds
07/25/2025 09:55:13 - INFO - __main__ - Training results saved to finetuned_models/d2pruning_10samples_alpaca/training_results.json
07/25/2025 09:55:13 - INFO - __main__ - Fine-tuning completed successfully!
07/25/2025 09:55:13 - INFO - __main__ - Fine-tuning complete.
