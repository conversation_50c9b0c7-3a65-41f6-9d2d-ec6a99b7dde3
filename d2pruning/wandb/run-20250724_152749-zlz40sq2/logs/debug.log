2025-07-24 15:27:49,071 INFO    MainThread:2274553 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-24 15:27:49,072 INFO    MainThread:2274553 [wandb_setup.py:_flush():80] Configure stats pid to 2274553
2025-07-24 15:27:49,072 INFO    MainThread:2274553 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-24 15:27:49,072 INFO    MainThread:2274553 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-24 15:27:49,072 INFO    MainThread:2274553 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-24 15:27:49,072 INFO    MainThread:2274553 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_152749-zlz40sq2/logs/debug.log
2025-07-24 15:27:49,072 INFO    MainThread:2274553 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_152749-zlz40sq2/logs/debug-internal.log
2025-07-24 15:27:49,073 INFO    MainThread:2274553 [wandb_init.py:init():830] calling init triggers
2025-07-24 15:27:49,073 INFO    MainThread:2274553 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'base_model': 'meta-llama/Llama-2-7b-hf', 'max_test_samples': 100, 'model_dirs': ['finetuned_models/random_10samples_alpaca', 'finetuned_models/moderate_10samples_alpaca', 'finetuned_models/k_center_10samples_alpaca'], 'task': 'evaluation', '_wandb': {}}
2025-07-24 15:27:49,073 INFO    MainThread:2274553 [wandb_init.py:init():871] starting backend
2025-07-24 15:27:49,087 INFO    MainThread:2274553 [wandb_init.py:init():874] sending inform_init request
2025-07-24 15:27:49,094 INFO    MainThread:2274553 [wandb_init.py:init():882] backend started and connected
2025-07-24 15:27:49,097 INFO    MainThread:2274553 [wandb_init.py:init():953] updated telemetry
2025-07-24 15:27:49,106 INFO    MainThread:2274553 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-24 15:27:49,781 INFO    MainThread:2274553 [wandb_init.py:init():1029] starting run threads in backend
2025-07-24 15:27:50,049 INFO    MainThread:2274553 [wandb_run.py:_console_start():2458] atexit reg
2025-07-24 15:27:50,050 INFO    MainThread:2274553 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-24 15:27:50,050 INFO    MainThread:2274553 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-24 15:27:50,050 INFO    MainThread:2274553 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-24 15:27:50,054 INFO    MainThread:2274553 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-24 16:45:28,969 INFO    MainThread:2274553 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/zlz40sq2
2025-07-24 16:45:28,969 INFO    MainThread:2274553 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-24 16:45:28,969 INFO    MainThread:2274553 [wandb_run.py:_restore():2405] restore
2025-07-24 16:45:28,970 INFO    MainThread:2274553 [wandb_run.py:_restore():2411] restore done
2025-07-24 16:45:32,716 INFO    MainThread:2274553 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-24 16:45:32,718 INFO    MainThread:2274553 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-24 16:45:32,720 INFO    MainThread:2274553 [wandb_run.py:_footer_sync_info():3864] logging synced files
