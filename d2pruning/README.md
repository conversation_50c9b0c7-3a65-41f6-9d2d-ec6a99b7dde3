# Data Selection Experiment for Dialog Summarization

This repository contains code for running experiments to compare different data selection methods for fine-tuning LLaMA-2-7b on dialog summarization tasks. The experiment evaluates how different data selection strategies affect model performance when training on a small subset of the DialogSum dataset.

## Overview

This experiment compares the following data selection methods:

1. **Random Selection**: Randomly selects samples from the training set
2. **Moderate Selection**: Selects samples with balanced diversity and representativeness
3. **K-Center Greedy**: Selects samples by maximizing coverage of the embedding space
4. **Diversity-Based**: Selects samples with highest diversity scores
5. **D2Pruning**: Selects samples based on training dynamics and graph density

## Setup

### Prerequisites

- Python 3.8+
- CUDA-compatible GPU
- Access to Hugging Face models (including LLaMA-2-7b)

### Installation

1. Clone this repository:
   ```bash
   git clone https://github.com/adymaharana/d2pruning.git
   cd d2pruning
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Weights & Biases Setup (Optional)

To track experiments with Weights & Biases:

1. Install wandb if not included in requirements:
   ```bash
   pip install wandb
   ```

2. Log in to your wandb account:
   ```bash
   wandb login
   ```

3. Set your API key as an environment variable:
   ```bash
   export WANDB_API_KEY=your_api_key_here
   ```

## Running the Experiment

### Option 1: All-in-One Script

The easiest way to run the complete experiment is using the setup script:

```bash
bash setup_and_run_experiment.sh
```

This script will:
1. Run all selection methods
2. Fine-tune LLaMA-2-7b on each selection
3. Evaluate all models
4. Save results

### Option 2: Step-by-Step Execution

If you prefer to run the experiment step by step:

#### 1. Data Selection

For standard selection methods:
```bash
cd d2pruning
python dialogsum_selection.py --selection_method [random|moderate|k_center|diversity] --num_samples 10 --visualize
```

For D2Pruning (requires pre-computed importance scores):
```bash
# First ensure you have importance scores
# Then run:
python dialogsum_selection.py --selection_method d2pruning --difficulty_score_method training_dynamics --difficulty_scores_path ../selected_samples/importance_scores.npy --num_samples 10 --visualize
```

If selection results don't include dialogues/summaries, prepare them:
```bash
python prepare_selection_data.py
```

#### 2. Fine-tuning

Fine-tune LLaMA-2-7b on the selected samples:
```bash
python finetune_llama.py --selection_results ./results/selection_results_[METHOD]_10.json --num_epochs 3 --format_type alpaca --output_dir ./finetuned_models
```

Replace `[METHOD]` with the selection method (random, moderate, k_center, diversity, or d2pruning).

#### 3. Evaluation

Evaluate the fine-tuned models:
```bash
python evaluate_summaries.py --model_dirs ./finetuned_models/[METHOD]_10samples_alpaca --base_model meta-llama/Llama-2-7b-hf --max_test_samples 500 --seed 42 --output_dir ./evaluation_results
```

To evaluate all models at once:
```bash
python evaluate_summaries.py --model_dirs ./finetuned_models/random_10samples_alpaca ./finetuned_models/moderate_10samples_alpaca ./finetuned_models/k_center_10samples_alpaca ./finetuned_models/diversity_10samples_alpaca ./finetuned_models/d2pruning_10samples_alpaca --base_model meta-llama/Llama-2-7b-hf --max_test_samples 500 --seed 42 --output_dir ./evaluation_results
```

## Results & Analysis

### Understanding the Results

After running the experiments, you can find results in:

- **Selection Results**: `./results/selection_results_*.json`
- **Training Results**: `./finetuned_models/*/training_results.json`
- **Evaluation Results**: `./evaluation_results/all_evaluation_results.json`
- **Comparison Summary**: `./evaluation_results/comparison_summary.json`
- **Visualizations**: `./experiment_results/visualizations/`

### Weights & Biases Dashboard

If you enabled wandb logging, you can analyze your results through the web dashboard:

1. Go to https://wandb.ai
2. Navigate to your project (default: "data-selection-experiments")
3. You'll see runs grouped by phase:
   - `data_selection`: Selection metrics and visualizations
   - `fine-tuning`: Training loss, learning rate, etc.
   - `evaluation`: ROUGE metrics for each model

### Key Metrics

The experiment measures:

- **ROUGE-1, ROUGE-2, ROUGE-L, ROUGE-Lsum**: Text summarization quality metrics
- **Training time**: Time taken to fine-tune each model
- **Final loss**: Final training loss achieved
- **Parameters**: Total and trainable parameters

### Analyzing Selected Samples

To understand what kinds of samples each method selected:

1. Check `selection_results_*.json` files for selected dialogues and summaries
2. View visualizations in `./experiment_results/visualizations/`
3. For D2Pruning, examine the importance scores to understand selection criteria

## Technical Details

### Implementation Notes

- **Embedding Models**: Uses sentence transformers (default: "all-mpnet-base-v2")
- **Fine-tuning**: Uses 4-bit quantization with LoRA for efficient fine-tuning
- **Prompting Format**: Uses Alpaca instruction format for summarization

### Files and Directories

- `dialogsum_selection.py`: Implements different selection strategies
- `finetune_llama.py`: Fine-tunes LLaMA-2-7b on selected samples
- `evaluate_summaries.py`: Evaluates models using ROUGE metrics
- `prepare_selection_data.py`: Processes selection results for fine-tuning
- `results/`: Contains selection results
- `finetuned_models/`: Contains fine-tuned models
- `evaluation_results/`: Contains evaluation results
- `experiment_results/`: Contains visualizations and detailed results
- `selected_samples/`: Contains standardized selection outputs

## Troubleshooting

### Common Issues

1. **Out of Memory Errors**: Reduce batch size or use more aggressive quantization
2. **Missing Dialogues/Summaries**: Run `prepare_selection_data.py` to add them
3. **WandB Connection Errors**: Check your API key and internet connection

### Logs

Check logs in the `logs/` directory for detailed error messages and progress reports.

## Citation

If you use this codebase for your research, please cite:

```
@misc{d2pruning2023,
  author = {Maharana, Adyasha},
  title = {D2Pruning: Data Selection for Efficient Fine-tuning},
  year = {2023},
  publisher = {GitHub},
  journal = {GitHub Repository},
  howpublished = {\url{https://github.com/adymaharana/d2pruning}}
}
```
