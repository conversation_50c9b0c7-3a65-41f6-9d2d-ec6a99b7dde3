07/24/2025 11:46:56 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 11:46:56 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/ts1egzio
07/24/2025 11:46:56 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 11:46:59 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [01:16<00:00, 38.43s/it]
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484
07/24/2025 11:48:17 - INFO - __main__ - Preparing training dataset...
Map: 100%|██████████| 10/10 [00:00<00:00, 24.16 examples/s]
07/24/2025 11:48:41 - INFO - __main__ - Starting fine-tuning...
07/24/2025 11:48:41 - INFO - wandb_logger - Logged training start for moderate to W&B
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 206, in train
    training_args = TrainingArguments(
TypeError: TrainingArguments.__init__() got an unexpected keyword argument 'evaluation_strategy'
