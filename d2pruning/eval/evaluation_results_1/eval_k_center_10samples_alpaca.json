{"selection_method": "k_center", "num_samples": 10, "format_type": "alpaca", "model_name": "meta-llama/Llama-2-7b-hf", "training_results": {"training_time": 20.1176860332489, "num_epochs": 3, "learning_rate": 0.0002, "num_samples": 10, "final_loss": 1.9723537498050265, "total_parameters": 3517190144, "trainable_parameters": 16777216}, "output_dir": "finetuned_models/k_center_10samples_alpaca", "evaluation_results": {"rouge_scores": {"rouge1": 0.06023942793208766, "rouge2": 0.006296356275303644, "rougeL": 0.051927049232520645, "rougeLsum": 0.053058977380177196}, "generation_time": 1516.7048225402832, "num_test_samples": 100, "generated_summaries": ["This dialogue is about the office's ban on the use of Instant Message programs during work hours. The dialogue's speaker is the office's manager, Mr. <PERSON>, and the dialogue's listener is his assistant, Ms. <PERSON>.", "Write a response to the above dialogue that summarizes the new policy.", "Write a response to the above dialogue.", "### Instruction:", "*Write a response that appropriately completes the request.*"], "reference_summaries": ["Ms<PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "Ms<PERSON> <PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations."]}}