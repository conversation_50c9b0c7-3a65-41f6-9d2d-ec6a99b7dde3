{"selection_method": "random", "num_samples": 10, "format_type": "alpaca", "model_name": "meta-llama/Llama-2-7b-hf", "training_results": {"training_time": 20.98527717590332, "num_epochs": 3, "learning_rate": 0.0002, "num_samples": 10, "final_loss": 1.9279904497994318, "total_parameters": 3517190144, "trainable_parameters": 16777216}, "output_dir": "finetuned_models/random_10samples_alpaca", "evaluation_results": {"rouge_scores": {"rouge1": 0.09724756161706458, "rouge2": 0.01838270580712098, "rougeL": 0.0819140179313125, "rougeLsum": 0.08245389980386897}, "generation_time": 1554.3468854427338, "num_test_samples": 100, "generated_summaries": ["> Dear Staff,", "### Notes", "Your response should be at least 300 words in length.", "Write a response to #Person1#, using the input provided.", "### Notes"], "reference_summaries": ["Ms<PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "Ms<PERSON> <PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations."]}}