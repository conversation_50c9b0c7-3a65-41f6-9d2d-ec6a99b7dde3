2025-07-23 12:33:15,241 INFO    MainThread:495941 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-23 12:33:15,241 INFO    MainThread:495941 [wandb_setup.py:_flush():80] Configure stats pid to 495941
2025-07-23 12:33:15,241 INFO    MainThread:495941 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-23 12:33:15,242 INFO    MainThread:495941 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-23 12:33:15,242 INFO    MainThread:495941 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-23 12:33:15,242 INFO    MainThread:495941 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250723_123315-ab4mjw96/logs/debug.log
2025-07-23 12:33:15,242 INFO    MainThread:495941 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250723_123315-ab4mjw96/logs/debug-internal.log
2025-07-23 12:33:15,242 INFO    MainThread:495941 [wandb_init.py:init():830] calling init triggers
2025-07-23 12:33:15,242 INFO    MainThread:495941 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_method': 'k_center', 'num_samples': 10, 'embedding_model': 'all-MiniLM-L6-v2', 'seed': 42, 'task': 'data_selection', '_wandb': {}}
2025-07-23 12:33:15,242 INFO    MainThread:495941 [wandb_init.py:init():871] starting backend
2025-07-23 12:33:15,254 INFO    MainThread:495941 [wandb_init.py:init():874] sending inform_init request
2025-07-23 12:33:15,262 INFO    MainThread:495941 [wandb_init.py:init():882] backend started and connected
2025-07-23 12:33:15,265 INFO    MainThread:495941 [wandb_init.py:init():953] updated telemetry
2025-07-23 12:33:15,275 INFO    MainThread:495941 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-23 12:33:16,013 INFO    MainThread:495941 [wandb_init.py:init():1029] starting run threads in backend
2025-07-23 12:33:16,285 INFO    MainThread:495941 [wandb_run.py:_console_start():2458] atexit reg
2025-07-23 12:33:16,285 INFO    MainThread:495941 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-23 12:33:16,285 INFO    MainThread:495941 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-23 12:33:16,285 INFO    MainThread:495941 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-23 12:33:16,289 INFO    MainThread:495941 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-23 12:33:22,349 INFO    MainThread:495941 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/ab4mjw96
2025-07-23 12:33:22,349 INFO    MainThread:495941 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-23 12:33:22,349 INFO    MainThread:495941 [wandb_run.py:_restore():2405] restore
2025-07-23 12:33:22,349 INFO    MainThread:495941 [wandb_run.py:_restore():2411] restore done
2025-07-23 12:33:24,090 INFO    MainThread:495941 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-23 12:33:24,091 INFO    MainThread:495941 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-23 12:33:24,092 INFO    MainThread:495941 [wandb_run.py:_footer_sync_info():3864] logging synced files
