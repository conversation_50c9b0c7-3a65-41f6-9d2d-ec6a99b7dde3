#!/bin/bash
# Run d2pruning selection with 3 epochs of fine-tuning

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Run the script
python run_d2pruning_3ep.py \
    --num_samples 10 \
    --format_type chat \
    --max_test_samples 50 \
    --batch_size 4 \
    --output_dir ./d2pruning_3ep_results

# Output where to find the results
echo "Experiment complete. Results available in ./d2pruning_3ep_results"
