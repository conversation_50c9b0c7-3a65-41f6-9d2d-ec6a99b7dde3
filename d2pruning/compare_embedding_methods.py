#!/usr/bin/env python3
"""
Compare different embedding methods for DialogSum data selection.

This script compares:
1. Sentence Transformer embeddings (all-MiniLM-L6-v2)
2. LLaMA-2-7b-hf embeddings

Shows why using the same model for embeddings and fine-tuning makes more sense.
"""

import os
import sys
import json
import time
import logging
import argparse
import numpy as np
from pathlib import Path
from typing import List, Dict, Any

import torch
from datasets import load_dataset
from transformers import set_seed

# Add d2pruning core modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
from data import CoresetSelection

from dialogsum_selection import DialogSumProcessor, EmbeddingExtractor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def compare_embeddings(num_samples: int = 100, cache_dir: str = "./cache"):
    """Compare different embedding methods."""
    
    logger.info("="*60)
    logger.info("COMPARING EMBEDDING METHODS FOR DIALOGSUM SELECTION")
    logger.info("="*60)
    
    # Initialize processor
    processor = DialogSumProcessor(cache_dir=cache_dir)
    
    # Load small subset of DialogSum for comparison
    logger.info("Loading DialogSum dataset...")
    train_data = processor.load_dialogsum("train")
    
    # Limit to num_samples for comparison
    if len(train_data['dialogues']) > num_samples:
        indices = list(range(num_samples))
        train_data = {
            'dialogues': [train_data['dialogues'][i] for i in indices],
            'summaries': [train_data['summaries'][i] for i in indices],
            'metadata': [train_data['metadata'][i] for i in indices]
        }
    
    logger.info(f"Using {len(train_data['dialogues'])} samples for comparison")
    
    # Combine dialogues and summaries for embedding
    combined_texts = [f"Dialogue: {d}\nSummary: {s}" for d, s in 
                     zip(train_data['dialogues'], train_data['summaries'])]
    
    results = {}
    
    # 1. Test Sentence Transformer embeddings
    logger.info("\n" + "="*40)
    logger.info("1. SENTENCE TRANSFORMER EMBEDDINGS")
    logger.info("="*40)
    
    try:
        st_extractor = EmbeddingExtractor(
            model_name="all-MiniLM-L6-v2", 
            cache_dir=cache_dir
        )
        
        start_time = time.time()
        st_embeddings = st_extractor.extract_embeddings(
            combined_texts, 
            f"dialogsum_train_combined_comparison_{num_samples}"
        )
        st_time = time.time() - start_time
        
        logger.info(f"✅ Sentence Transformer completed")
        logger.info(f"   Embedding shape: {st_embeddings.shape}")
        logger.info(f"   Time taken: {st_time:.2f} seconds")
        logger.info(f"   Embedding dimension: {st_embeddings.shape[1]}")
        
        results['sentence_transformer'] = {
            'shape': st_embeddings.shape,
            'time': st_time,
            'dimension': st_embeddings.shape[1],
            'model': "all-MiniLM-L6-v2"
        }
        
    except Exception as e:
        logger.error(f"❌ Sentence Transformer failed: {e}")
        results['sentence_transformer'] = {'error': str(e)}
    
    # 2. Test LLaMA embeddings
    logger.info("\n" + "="*40)
    logger.info("2. LLAMA-2-7B-HF EMBEDDINGS")
    logger.info("="*40)
    
    try:
        llama_extractor = EmbeddingExtractor(
            model_name="meta-llama/Llama-2-7b-hf", 
            cache_dir=cache_dir
        )
        
        start_time = time.time()
        llama_embeddings = llama_extractor.extract_embeddings(
            combined_texts, 
            f"dialogsum_train_combined_llama_comparison_{num_samples}"
        )
        llama_time = time.time() - start_time
        
        logger.info(f"✅ LLaMA embeddings completed")
        logger.info(f"   Embedding shape: {llama_embeddings.shape}")
        logger.info(f"   Time taken: {llama_time:.2f} seconds")
        logger.info(f"   Embedding dimension: {llama_embeddings.shape[1]}")
        
        results['llama'] = {
            'shape': llama_embeddings.shape,
            'time': llama_time,
            'dimension': llama_embeddings.shape[1],
            'model': "meta-llama/Llama-2-7b-hf"
        }
        
    except Exception as e:
        logger.error(f"❌ LLaMA embeddings failed: {e}")
        results['llama'] = {'error': str(e)}
    
    # 3. Compare selection quality (if both succeeded)
    if 'sentence_transformer' in results and 'llama' in results and \
       'error' not in results['sentence_transformer'] and 'error' not in results['llama']:
        
        logger.info("\n" + "="*40)
        logger.info("3. SELECTION QUALITY COMPARISON")
        logger.info("="*40)
        
        # Test moderate selection with both embeddings
        selection_size = min(10, len(train_data['dialogues']) // 2)
        
        for method_name, embeddings in [
            ("Sentence Transformer", st_embeddings),
            ("LLaMA", llama_embeddings)
        ]:
            logger.info(f"\n{method_name} Selection:")
            
            # Create embedding-based scores
            from dialogsum_selection import create_embedding_based_scores
            embedding_scores = create_embedding_based_scores(
                embeddings, train_data['dialogues'], train_data['summaries']
            )
            
            ratio = selection_size / len(train_data['dialogues'])
            selected_indices = CoresetSelection.moderate_selection(
                data_score=embedding_scores,
                ratio=ratio,
                features=torch.from_numpy(embeddings).float()
            )
            
            logger.info(f"   Selected {len(selected_indices)} samples")
            logger.info(f"   Selected indices: {selected_indices[:5].tolist()}...")
            
            # Analyze selected samples
            selected_dialogues = [train_data['dialogues'][i] for i in selected_indices]
            avg_length = np.mean([len(d.split()) for d in selected_dialogues])
            logger.info(f"   Average dialogue length: {avg_length:.1f} words")
    
    # 4. Summary and recommendations
    logger.info("\n" + "="*60)
    logger.info("COMPARISON SUMMARY")
    logger.info("="*60)
    
    if 'sentence_transformer' in results and 'error' not in results['sentence_transformer']:
        st_result = results['sentence_transformer']
        logger.info(f"Sentence Transformer (all-MiniLM-L6-v2):")
        logger.info(f"  ✅ Dimension: {st_result['dimension']}")
        logger.info(f"  ✅ Time: {st_result['time']:.2f}s")
        logger.info(f"  ✅ Memory: Low")
        logger.info(f"  ❌ Consistency: Different from fine-tuning model")
    
    if 'llama' in results and 'error' not in results['llama']:
        llama_result = results['llama']
        logger.info(f"\nLLaMA-2-7b-hf:")
        logger.info(f"  ✅ Dimension: {llama_result['dimension']}")
        logger.info(f"  ✅ Time: {llama_result['time']:.2f}s")
        logger.info(f"  ⚠️  Memory: Higher")
        logger.info(f"  ✅ Consistency: Same as fine-tuning model")
    
    logger.info(f"\n🎯 RECOMMENDATION:")
    logger.info(f"   Use LLaMA-2-7b-hf for embeddings because:")
    logger.info(f"   1. Same model used for selection and fine-tuning")
    logger.info(f"   2. Better representation alignment")
    logger.info(f"   3. More theoretically sound approach")
    logger.info(f"   4. Selection based on actual target model's understanding")
    
    return results


def main():
    parser = argparse.ArgumentParser(description="Compare embedding methods for DialogSum selection")
    parser.add_argument("--num_samples", type=int, default=100,
                       help="Number of samples to use for comparison")
    parser.add_argument("--cache_dir", type=str, default="./cache",
                       help="Cache directory")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    
    args = parser.parse_args()
    
    # Set seed
    set_seed(args.seed)
    
    # Run comparison
    results = compare_embeddings(args.num_samples, args.cache_dir)
    
    # Save results
    output_file = Path(args.cache_dir) / "embedding_comparison_results.json"
    with open(output_file, 'w') as f:
        # Convert numpy arrays to lists for JSON serialization
        json_results = {}
        for method, result in results.items():
            if 'shape' in result:
                json_results[method] = {
                    **result,
                    'shape': list(result['shape'])
                }
            else:
                json_results[method] = result
        
        json.dump(json_results, f, indent=2)
    
    logger.info(f"\nResults saved to: {output_file}")


if __name__ == "__main__":
    main()
