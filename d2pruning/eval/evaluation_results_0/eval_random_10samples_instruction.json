{"selection_method": "random", "num_samples": 10, "format_type": "instruction", "model_name": "meta-llama/Llama-2-7b-hf", "training_results": {"training_time": 25.754123210906982, "num_epochs": 3, "learning_rate": 0.0002, "num_samples": 10, "final_loss": 1.810667249891493, "total_parameters": 3517190144, "trainable_parameters": 16777216}, "output_dir": "finetuned_models/random_10samples_instruction", "evaluation_results": {"rouge_scores": {"rouge1": 0.13429257959048574, "rouge2": 0.03040100460224757, "rougeL": 0.11848402506372993, "rougeLsum": 0.11716569192037977}, "generation_time": 1760.6985383033752, "num_test_samples": 100, "generated_summaries": [">#Person2#: Yes, sir...", "The dialogue is summarized as follows:", "The summary of the dialogue above is:", "#Person1#: Great!", "- I'm going to start taking the subway to work."], "reference_summaries": ["Ms<PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "Ms<PERSON> <PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations."]}}