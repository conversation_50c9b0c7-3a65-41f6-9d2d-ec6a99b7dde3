#!/usr/bin/env python3
"""
Clear cache and force regeneration of DialogSum selection results.

This script helps when you want to regenerate selection results with 
different embedding models (e.g., switching from sentence transformers to LLaMA).
"""

import os
import sys
import shutil
import logging
import argparse
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def clear_cache_directories(base_dir: str = "./"):
    """Clear cache and results directories."""
    base_path = Path(base_dir)
    
    # Directories to clear
    dirs_to_clear = [
        base_path / "cache",
        base_path / "results",
        base_path / "finetuned_models",
        base_path / "evaluation_results"
    ]
    
    for dir_path in dirs_to_clear:
        if dir_path.exists():
            logger.info(f"Clearing directory: {dir_path}")
            shutil.rmtree(dir_path)
            logger.info(f"✅ Cleared: {dir_path}")
        else:
            logger.info(f"Directory doesn't exist: {dir_path}")
    
    logger.info("Cache clearing completed!")


def clear_specific_embeddings(embedding_model: str, base_dir: str = "./"):
    """Clear only embeddings for a specific model."""
    cache_dir = Path(base_dir) / "cache"
    
    if not cache_dir.exists():
        logger.info("No cache directory found")
        return
    
    # Find embedding files for the specific model
    model_name_safe = embedding_model.replace('/', '_')
    pattern = f"embeddings_*_{model_name_safe}.npy"
    
    embedding_files = list(cache_dir.glob(pattern))
    
    if embedding_files:
        logger.info(f"Found {len(embedding_files)} embedding files for {embedding_model}")
        for file_path in embedding_files:
            logger.info(f"Removing: {file_path}")
            file_path.unlink()
        logger.info(f"✅ Cleared embeddings for {embedding_model}")
    else:
        logger.info(f"No embedding files found for {embedding_model}")


def clear_selection_results(base_dir: str = "./"):
    """Clear only selection results (keep embeddings)."""
    results_dir = Path(base_dir) / "results"
    
    if not results_dir.exists():
        logger.info("No results directory found")
        return
    
    selection_files = list(results_dir.glob("selection_results_*.json"))
    
    if selection_files:
        logger.info(f"Found {len(selection_files)} selection result files")
        for file_path in selection_files:
            logger.info(f"Removing: {file_path}")
            file_path.unlink()
        logger.info("✅ Cleared selection results")
    else:
        logger.info("No selection result files found")


def show_cache_status(base_dir: str = "./"):
    """Show current cache status."""
    base_path = Path(base_dir)
    
    logger.info("="*60)
    logger.info("CURRENT CACHE STATUS")
    logger.info("="*60)
    
    # Check cache directory
    cache_dir = base_path / "cache"
    if cache_dir.exists():
        embedding_files = list(cache_dir.glob("embeddings_*.npy"))
        logger.info(f"Cache directory: {cache_dir}")
        logger.info(f"  Embedding files: {len(embedding_files)}")
        for file_path in embedding_files:
            size_mb = file_path.stat().st_size / (1024 * 1024)
            logger.info(f"    {file_path.name} ({size_mb:.1f} MB)")
    else:
        logger.info("Cache directory: Not found")
    
    # Check results directory
    results_dir = base_path / "results"
    if results_dir.exists():
        selection_files = list(results_dir.glob("selection_results_*.json"))
        logger.info(f"Results directory: {results_dir}")
        logger.info(f"  Selection files: {len(selection_files)}")
        for file_path in selection_files:
            logger.info(f"    {file_path.name}")
    else:
        logger.info("Results directory: Not found")
    
    # Check fine-tuned models
    models_dir = base_path / "finetuned_models"
    if models_dir.exists():
        model_dirs = [d for d in models_dir.iterdir() if d.is_dir()]
        logger.info(f"Fine-tuned models: {len(model_dirs)}")
        for model_dir in model_dirs:
            logger.info(f"    {model_dir.name}")
    else:
        logger.info("Fine-tuned models: Not found")


def main():
    parser = argparse.ArgumentParser(description="Clear cache and force regeneration")
    parser.add_argument("--action", type=str, 
                       choices=["clear_all", "clear_embeddings", "clear_results", "status"],
                       default="status",
                       help="Action to perform")
    parser.add_argument("--embedding_model", type=str, default="all-MiniLM-L6-v2",
                       help="Embedding model to clear (for clear_embeddings)")
    parser.add_argument("--base_dir", type=str, default="./",
                       help="Base directory")
    parser.add_argument("--confirm", action="store_true",
                       help="Confirm destructive actions")
    
    args = parser.parse_args()
    
    if args.action == "status":
        show_cache_status(args.base_dir)
    
    elif args.action == "clear_all":
        if not args.confirm:
            logger.warning("This will clear ALL cache, results, and models!")
            logger.warning("Use --confirm to proceed")
            return
        clear_cache_directories(args.base_dir)
    
    elif args.action == "clear_embeddings":
        if not args.confirm:
            logger.warning(f"This will clear embeddings for {args.embedding_model}!")
            logger.warning("Use --confirm to proceed")
            return
        clear_specific_embeddings(args.embedding_model, args.base_dir)
    
    elif args.action == "clear_results":
        if not args.confirm:
            logger.warning("This will clear selection results!")
            logger.warning("Use --confirm to proceed")
            return
        clear_selection_results(args.base_dir)
    
    logger.info("\n" + "="*60)
    logger.info("NEXT STEPS")
    logger.info("="*60)
    logger.info("To regenerate with LLaMA embeddings:")
    logger.info("python run_dialogsum_experiment.py --force_regenerate")
    logger.info("")
    logger.info("Or to clear everything and start fresh:")
    logger.info("python run_dialogsum_experiment.py --clear_cache")


if __name__ == "__main__":
    main()
