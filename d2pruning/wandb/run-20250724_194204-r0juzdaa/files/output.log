07/24/2025 19:42:05 - INFO - wandb_logger - Initialized W&B run: selection_random_10
07/24/2025 19:42:05 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/r0juzdaa
07/24/2025 19:42:05 - INFO - __main__ - Starting DialogSum data selection pipeline
07/24/2025 19:42:05 - INFO - __main__ - Selection method: random
07/24/2025 19:42:05 - INFO - __main__ - Number of samples: 10
07/24/2025 19:42:05 - INFO - __main__ - Embedding model: all-MiniLM-L6-v2
07/24/2025 19:42:05 - INFO - __main__ - Loading DialogSum dataset...
07/24/2025 19:42:05 - INFO - __main__ - Loading cached DialogSum train dataset from cache/dialogsum_train.pkl
07/24/2025 19:42:05 - INFO - __main__ - Loading cached DialogSum test dataset from cache/dialogsum_test.pkl
07/24/2025 19:42:05 - INFO - __main__ - Train dataset size: 12460
07/24/2025 19:42:05 - INFO - __main__ - Test dataset size: 1500
07/24/2025 19:42:06 - INFO - __main__ - Preprocessed 12460 training samples
07/24/2025 19:42:06 - INFO - __main__ - Average dialogue length: 131.0 words
07/24/2025 19:42:06 - INFO - __main__ - Average summary length: 22.9 words
07/24/2025 19:42:06 - INFO - wandb_logger - Logged dataset information to W&B
07/24/2025 19:42:06 - INFO - __main__ - Extracting embeddings...
07/24/2025 19:42:06 - INFO - __main__ - Loading cached embeddings from cache/embeddings_dialogsum_train_combined_all-MiniLM-L6-v2.npy
07/24/2025 19:42:06 - INFO - __main__ - Extracted embeddings shape: (12460, 384)
07/24/2025 19:42:06 - INFO - wandb_logger - Logged embedding information to W&B
07/24/2025 19:42:06 - INFO - __main__ - Performing random selection...
Random selection.
07/24/2025 19:42:06 - INFO - __main__ - Selection completed in 0.00 seconds
07/24/2025 19:42:06 - INFO - __main__ - Selected indices: [ 1702  9258  2966  3452  8086  8855  3148  1881  3030 10283]
07/24/2025 19:42:06 - INFO - wandb_logger - Logged selection results for random to W&B
