07/25/2025 16:30:56 - INFO - __main__ - Loading model from d2pruning/finetuned_models/d2pruning_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 16:30:57 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards:  50%|█████     | 1/2 [00:19<00:19, 19.74s/it]Exception ignored in: <generator object tqdm.__iter__ at 0x73eea438bbc0>Exception ignored in sys.unraisablehook: <built-in function unraisablehook>
