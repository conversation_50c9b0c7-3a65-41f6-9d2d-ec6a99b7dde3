07/24/2025 19:45:05 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 19:45:05 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/hjjgbyq7
07/24/2025 19:45:05 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 19:45:08 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
