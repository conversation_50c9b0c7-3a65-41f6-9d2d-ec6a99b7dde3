07/24/2025 12:25:49 - INFO - wandb_logger - Initialized W&B run: eval_3models
07/24/2025 12:25:49 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/rsbgmv10
Downloading builder script: 6.27kB [00:00, 5.32MB/s]
07/24/2025 12:25:56 - INFO - __main__ - Loading DialogSum test dataset...
Generating train split: 100%|██████████| 12460/12460 [00:00<00:00, 28763.38 examples/s]
Generating validation split: 100%|██████████| 500/500 [00:00<00:00, 22754.32 examples/s]
Generating test split: 100%|██████████| 1500/1500 [00:00<00:00, 44928.38 examples/s]
07/24/2025 12:26:01 - INFO - __main__ - Loaded 1500 test samples
07/24/2025 12:26:01 - INFO - __main__ - Evaluating model: random_10samples_instruction
07/24/2025 12:26:01 - INFO - wandb_logger - Logged evaluation start for random to W&B
07/24/2025 12:26:01 - INFO - __main__ - Loading fine-tuned model from finetuned_models/random_10samples_instruction
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/24/2025 12:26:04 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [01:11<00:00, 35.72s/it]
07/24/2025 12:27:17 - INFO - __main__ - Generating summaries for 100 dialogues...
Generating summaries:   0%|          | 0/100 [00:00<?, ?it/s]/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/bitsandbytes/nn/modules.py:457: UserWarning: Input type into Linear4bit is torch.float16, but bnb_4bit_compute_dtype=torch.float32 (default). This will lead to slow inference or training speed.
  warnings.warn(
Generating summaries: 100%|██████████| 100/100 [29:20<00:00, 17.61s/it]
07/24/2025 12:56:38 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 12:56:38 - INFO - absl - Using default tokenizer.
07/24/2025 12:56:39 - INFO - __main__ - ROUGE Scores:
07/24/2025 12:56:39 - INFO - __main__ -   rouge1: 0.1343
07/24/2025 12:56:39 - INFO - __main__ -   rouge2: 0.0304
07/24/2025 12:56:39 - INFO - __main__ -   rougeL: 0.1185
07/24/2025 12:56:39 - INFO - __main__ -   rougeLsum: 0.1172
07/24/2025 12:56:40 - INFO - wandb_logger - Logged evaluation results for random to W&B
07/24/2025 12:56:40 - INFO - __main__ - Results saved to evaluation_results/eval_random_10samples_instruction.json
07/24/2025 12:56:40 - INFO - __main__ - Evaluating model: moderate_10samples_instruction
07/24/2025 12:56:40 - INFO - wandb_logger - Logged evaluation start for moderate to W&B
07/24/2025 12:56:40 - INFO - __main__ - Loading fine-tuned model from finetuned_models/moderate_10samples_instruction
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/24/2025 12:56:41 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:25<00:00, 12.55s/it]
07/24/2025 12:57:08 - INFO - __main__ - Generating summaries for 100 dialogues...
Generating summaries: 100%|██████████| 100/100 [28:13<00:00, 16.94s/it]
07/24/2025 13:25:22 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 13:25:22 - INFO - absl - Using default tokenizer.
07/24/2025 13:25:22 - INFO - __main__ - ROUGE Scores:
07/24/2025 13:25:22 - INFO - __main__ -   rouge1: 0.1153
07/24/2025 13:25:22 - INFO - __main__ -   rouge2: 0.0169
07/24/2025 13:25:22 - INFO - __main__ -   rougeL: 0.0976
07/24/2025 13:25:22 - INFO - __main__ -   rougeLsum: 0.0979
07/24/2025 13:25:23 - INFO - wandb_logger - Logged evaluation results for moderate to W&B
07/24/2025 13:25:23 - INFO - __main__ - Results saved to evaluation_results/eval_moderate_10samples_instruction.json
07/24/2025 13:25:23 - INFO - __main__ - Evaluating model: k_center_10samples_instruction
07/24/2025 13:25:23 - INFO - wandb_logger - Logged evaluation start for k_center to W&B
07/24/2025 13:25:23 - INFO - __main__ - Loading fine-tuned model from finetuned_models/k_center_10samples_instruction
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/24/2025 13:25:24 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:25<00:00, 12.56s/it]
07/24/2025 13:25:51 - INFO - __main__ - Generating summaries for 100 dialogues...
Generating summaries: 100%|██████████| 100/100 [28:36<00:00, 17.17s/it]
07/24/2025 13:54:28 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 13:54:28 - INFO - absl - Using default tokenizer.
07/24/2025 13:54:29 - INFO - __main__ - ROUGE Scores:
07/24/2025 13:54:29 - INFO - __main__ -   rouge1: 0.1273
07/24/2025 13:54:29 - INFO - __main__ -   rouge2: 0.0140
07/24/2025 13:54:29 - INFO - __main__ -   rougeL: 0.1108
07/24/2025 13:54:29 - INFO - __main__ -   rougeLsum: 0.1111
07/24/2025 13:54:29 - INFO - wandb_logger - Logged evaluation results for k_center to W&B
07/24/2025 13:54:29 - INFO - __main__ - Results saved to evaluation_results/eval_k_center_10samples_instruction.json
07/24/2025 13:54:29 - INFO - wandb_logger - Logged comparison summary to W&B
