 33%|█████████████████████                                          | 3/9 [00:06<00:11,  1.95s/it]Saving model checkpoint to finetuned_models/d2pruning_10samples_chat/checkpoint-3
{'loss': 1.7603, 'grad_norm': 0.3396610617637634, 'learning_rate': 0.0, 'epoch': 0.4}
{'loss': 2.181, 'grad_norm': 0.6912360787391663, 'learning_rate': 2.0000000000000003e-06, 'epoch': 0.8}
{'loss': 2.0219, 'grad_norm': 0.6050516963005066, 'learning_rate': 4.000000000000001e-06, 'epoch': 1.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in finetuned_models/d2pruning_10samples_chat/checkpoint-3/tokenizer_config.json
Special tokens file saved in finetuned_models/d2pruning_10samples_chat/checkpoint-3/special_tokens_map.json
 67%|██████████████████████████████████████████                     | 6/9 [00:12<00:05,  1.80s/it]Saving model checkpoint to finetuned_models/d2pruning_10samples_chat/checkpoint-6
{'loss': 1.9006, 'grad_norm': 0.5052271485328674, 'learning_rate': 6e-06, 'epoch': 1.4}
{'loss': 2.0097, 'grad_norm': 0.47539621591567993, 'learning_rate': 8.000000000000001e-06, 'epoch': 1.8}
{'loss': 1.7752, 'grad_norm': 0.45131397247314453, 'learning_rate': 1e-05, 'epoch': 2.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in finetuned_models/d2pruning_10samples_chat/checkpoint-6/tokenizer_config.json
Special tokens file saved in finetuned_models/d2pruning_10samples_chat/checkpoint-6/special_tokens_map.json
100%|███████████████████████████████████████████████████████████████| 9/9 [00:19<00:00,  1.91s/it]Saving model checkpoint to finetuned_models/d2pruning_10samples_chat/checkpoint-9
{'loss': 1.7874, 'grad_norm': 0.4058825373649597, 'learning_rate': 1.2e-05, 'epoch': 2.4}
{'loss': 1.9265, 'grad_norm': 0.46386995911598206, 'learning_rate': 1.4000000000000001e-05, 'epoch': 2.8}
{'loss': 2.3285, 'grad_norm': 0.9260597229003906, 'learning_rate': 1.6000000000000003e-05, 'epoch': 3.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in finetuned_models/d2pruning_10samples_chat/checkpoint-9/tokenizer_config.json
Special tokens file saved in finetuned_models/d2pruning_10samples_chat/checkpoint-9/special_tokens_map.json
