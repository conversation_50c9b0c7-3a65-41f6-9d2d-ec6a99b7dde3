07/25/2025 08:01:33 - INFO - __main__ - Loading model from d2pruning/finetuned_models/random_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 08:01:33 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:23<00:00, 11.89s/it]
07/25/2025 08:02:00 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 08:02:00 - INFO - __main__ - Starting summary generation...
Generating Summaries: 100%|██████████| 13/13 [08:16<00:00, 38.21s/it]
07/25/2025 08:10:16 - INFO - __main__ - Generated 50 summaries.
07/25/2025 08:10:16 - INFO - __main__ - Computing and logging metrics...
Calculating ROUGE:   0%|          | 0/5 [00:00<?, ?it/s]07/25/2025 08:10:16 - INFO - absl - Using default tokenizer.
07/25/2025 08:10:17 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  20%|██        | 1/5 [00:00<00:02,  1.69it/s]07/25/2025 08:10:17 - INFO - absl - Using default tokenizer.
07/25/2025 08:10:17 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  40%|████      | 2/5 [00:01<00:01,  1.55it/s]07/25/2025 08:10:18 - INFO - absl - Using default tokenizer.
07/25/2025 08:10:18 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  60%|██████    | 3/5 [00:02<00:01,  1.46it/s]07/25/2025 08:10:18 - INFO - absl - Using default tokenizer.
07/25/2025 08:10:19 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  80%|████████  | 4/5 [00:02<00:00,  1.35it/s]07/25/2025 08:10:19 - INFO - absl - Using default tokenizer.
07/25/2025 08:10:20 - INFO - absl - Using default tokenizer.
Calculating ROUGE: 100%|██████████| 5/5 [00:03<00:00,  1.32it/s]
07/25/2025 08:10:20 - INFO - absl - Using default tokenizer.
07/25/2025 08:10:21 - INFO - __main__ - Final ROUGE scores: {'rouge1': np.float64(0.18456906748915186), 'rouge2': np.float64(0.03580183142890283), 'rougeL': np.float64(0.13382859753358253), 'rougeLsum': np.float64(0.15283250500950246)}
