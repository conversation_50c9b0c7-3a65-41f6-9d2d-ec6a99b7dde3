07/25/2025 07:34:38 - INFO - __main__ - Loading model from d2pruning/finetuned_models/diversity_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 07:34:40 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:32<00:00, 16.24s/it]
07/25/2025 07:35:14 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 07:35:14 - INFO - __main__ - Starting summary generation...
Generating Summaries:   0%|          | 0/13 [00:00<?, ?it/s]/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/bitsandbytes/nn/modules.py:457: UserWarning: Input type into Linear4bit is torch.float16, but bnb_4bit_compute_dtype=torch.float32 (default). This will lead to slow inference or training speed.
  warnings.warn(
Generating Summaries: 100%|██████████| 13/13 [08:19<00:00, 38.41s/it]
07/25/2025 07:43:34 - INFO - __main__ - Generated 50 summaries.
07/25/2025 07:43:34 - INFO - __main__ - Computing and logging metrics...
Calculating ROUGE:   0%|          | 0/5 [00:00<?, ?it/s]07/25/2025 07:43:34 - INFO - absl - Using default tokenizer.
07/25/2025 07:43:34 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  20%|██        | 1/5 [00:00<00:03,  1.29it/s]07/25/2025 07:43:35 - INFO - absl - Using default tokenizer.
07/25/2025 07:43:35 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  40%|████      | 2/5 [00:01<00:02,  1.30it/s]07/25/2025 07:43:35 - INFO - absl - Using default tokenizer.
07/25/2025 07:43:36 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  60%|██████    | 3/5 [00:02<00:01,  1.30it/s]07/25/2025 07:43:36 - INFO - absl - Using default tokenizer.
07/25/2025 07:43:36 - INFO - absl - Using default tokenizer.
Calculating ROUGE:  80%|████████  | 4/5 [00:03<00:00,  1.26it/s]07/25/2025 07:43:37 - INFO - absl - Using default tokenizer.
