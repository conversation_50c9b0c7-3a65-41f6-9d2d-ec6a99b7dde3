07/24/2025 15:27:50 - INFO - wandb_logger - Initialized W&B run: eval_3models
07/24/2025 15:27:50 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/zlz40sq2
07/24/2025 15:27:53 - INFO - __main__ - Loading DialogSum test dataset...
07/24/2025 15:27:57 - INFO - __main__ - Loaded 1500 test samples
07/24/2025 15:27:58 - INFO - __main__ - Evaluating model: random_10samples_alpaca
07/24/2025 15:27:58 - INFO - wandb_logger - Logged evaluation start for random to W&B
07/24/2025 15:27:58 - INFO - __main__ - Loading fine-tuned model from finetuned_models/random_10samples_alpaca
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/24/2025 15:28:00 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:46<00:00, 23.49s/it]
07/24/2025 15:28:49 - INFO - __main__ - Generating summaries for 100 dialogues...
Generating summaries:   0%|          | 0/100 [00:00<?, ?it/s]/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/bitsandbytes/nn/modules.py:457: UserWarning: Input type into Linear4bit is torch.float16, but bnb_4bit_compute_dtype=torch.float32 (default). This will lead to slow inference or training speed.
  warnings.warn(
Generating summaries:  49%|████▉     | 49/100 [12:18<11:42, 13.77s/it]07/24/2025 15:41:26 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 15:41:27 - INFO - absl - Using default tokenizer.
07/24/2025 15:41:27 - INFO - __main__ - ROUGE Scores:
07/24/2025 15:41:27 - INFO - __main__ -   rouge1: 0.0686
07/24/2025 15:41:27 - INFO - __main__ -   rouge2: 0.0440
07/24/2025 15:41:27 - INFO - __main__ -   rougeL: 0.0619
07/24/2025 15:41:27 - INFO - __main__ -   rougeLsum: 0.0675
07/24/2025 15:41:27 - INFO - __main__ - Progress (50/100): ROUGE-1: 0.0686
Generating summaries:  99%|█████████▉| 99/100 [25:34<00:18, 18.61s/it]07/24/2025 15:54:43 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 15:54:43 - INFO - absl - Using default tokenizer.
07/24/2025 15:54:43 - INFO - __main__ - ROUGE Scores:
07/24/2025 15:54:43 - INFO - __main__ -   rouge1: 0.1026
07/24/2025 15:54:43 - INFO - __main__ -   rouge2: 0.0541
07/24/2025 15:54:43 - INFO - __main__ -   rougeL: 0.0889
07/24/2025 15:54:43 - INFO - __main__ -   rougeLsum: 0.1008
07/24/2025 15:54:43 - INFO - __main__ - Progress (100/100): ROUGE-1: 0.1026
Generating summaries: 100%|██████████| 100/100 [25:54<00:00, 15.54s/it]
07/24/2025 15:54:43 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 15:54:43 - INFO - absl - Using default tokenizer.
07/24/2025 15:54:44 - INFO - __main__ - ROUGE Scores:
07/24/2025 15:54:44 - INFO - __main__ -   rouge1: 0.0972
07/24/2025 15:54:44 - INFO - __main__ -   rouge2: 0.0184
07/24/2025 15:54:44 - INFO - __main__ -   rougeL: 0.0819
07/24/2025 15:54:44 - INFO - __main__ -   rougeLsum: 0.0825
07/24/2025 15:54:45 - INFO - wandb_logger - Logged evaluation results for random to W&B
07/24/2025 15:54:45 - INFO - __main__ - Results saved to evaluation_results/eval_random_10samples_alpaca.json
07/24/2025 15:54:45 - INFO - __main__ - Evaluating model: moderate_10samples_alpaca
07/24/2025 15:54:45 - INFO - wandb_logger - Logged evaluation start for moderate to W&B
07/24/2025 15:54:45 - INFO - __main__ - Loading fine-tuned model from finetuned_models/moderate_10samples_alpaca
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/24/2025 15:54:46 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:23<00:00, 11.99s/it]
07/24/2025 15:55:12 - INFO - __main__ - Generating summaries for 100 dialogues...
Generating summaries:  49%|████▉     | 49/100 [12:34<08:46, 10.33s/it]07/24/2025 16:08:05 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 16:08:05 - INFO - absl - Using default tokenizer.
07/24/2025 16:08:06 - INFO - __main__ - ROUGE Scores:
07/24/2025 16:08:06 - INFO - __main__ -   rouge1: 0.0883
07/24/2025 16:08:06 - INFO - __main__ -   rouge2: 0.0422
07/24/2025 16:08:06 - INFO - __main__ -   rougeL: 0.0720
07/24/2025 16:08:06 - INFO - __main__ -   rougeLsum: 0.0822
07/24/2025 16:08:06 - INFO - __main__ - Progress (50/100): ROUGE-1: 0.0883
Generating summaries:  99%|█████████▉| 99/100 [24:10<00:12, 12.72s/it]07/24/2025 16:19:42 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 16:19:42 - INFO - absl - Using default tokenizer.
07/24/2025 16:19:43 - INFO - __main__ - ROUGE Scores:
07/24/2025 16:19:43 - INFO - __main__ -   rouge1: 0.0917
07/24/2025 16:19:43 - INFO - __main__ -   rouge2: 0.0498
07/24/2025 16:19:43 - INFO - __main__ -   rougeL: 0.0789
07/24/2025 16:19:43 - INFO - __main__ -   rougeLsum: 0.0891
07/24/2025 16:19:43 - INFO - __main__ - Progress (100/100): ROUGE-1: 0.0917
Generating summaries: 100%|██████████| 100/100 [24:30<00:00, 14.70s/it]
07/24/2025 16:19:43 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 16:19:43 - INFO - absl - Using default tokenizer.
07/24/2025 16:19:43 - INFO - __main__ - ROUGE Scores:
07/24/2025 16:19:43 - INFO - __main__ -   rouge1: 0.0799
07/24/2025 16:19:43 - INFO - __main__ -   rouge2: 0.0156
07/24/2025 16:19:43 - INFO - __main__ -   rougeL: 0.0684
07/24/2025 16:19:43 - INFO - __main__ -   rougeLsum: 0.0685
07/24/2025 16:19:43 - INFO - wandb_logger - Logged evaluation results for moderate to W&B
07/24/2025 16:19:43 - INFO - __main__ - Results saved to evaluation_results/eval_moderate_10samples_alpaca.json
07/24/2025 16:19:43 - INFO - __main__ - Evaluating model: k_center_10samples_alpaca
07/24/2025 16:19:43 - INFO - wandb_logger - Logged evaluation start for k_center to W&B
07/24/2025 16:19:43 - INFO - __main__ - Loading fine-tuned model from finetuned_models/k_center_10samples_alpaca
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/24/2025 16:19:45 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:24<00:00, 12.00s/it]
07/24/2025 16:20:11 - INFO - __main__ - Generating summaries for 100 dialogues...
Generating summaries:  49%|████▉     | 49/100 [12:33<14:27, 17.00s/it]07/24/2025 16:33:03 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 16:33:03 - INFO - absl - Using default tokenizer.
07/24/2025 16:33:03 - INFO - __main__ - ROUGE Scores:
07/24/2025 16:33:03 - INFO - __main__ -   rouge1: 0.0444
07/24/2025 16:33:03 - INFO - __main__ -   rouge2: 0.0249
07/24/2025 16:33:03 - INFO - __main__ -   rougeL: 0.0419
07/24/2025 16:33:03 - INFO - __main__ -   rougeLsum: 0.0441
07/24/2025 16:33:03 - INFO - __main__ - Progress (50/100): ROUGE-1: 0.0444
Generating summaries:  99%|█████████▉| 99/100 [25:09<00:16, 16.20s/it]07/24/2025 16:45:27 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 16:45:27 - INFO - absl - Using default tokenizer.
07/24/2025 16:45:27 - INFO - __main__ - ROUGE Scores:
07/24/2025 16:45:27 - INFO - __main__ -   rouge1: 0.0653
07/24/2025 16:45:27 - INFO - __main__ -   rouge2: 0.0364
07/24/2025 16:45:27 - INFO - __main__ -   rougeL: 0.0572
07/24/2025 16:45:27 - INFO - __main__ -   rougeLsum: 0.0621
07/24/2025 16:45:27 - INFO - __main__ - Progress (100/100): ROUGE-1: 0.0653
Generating summaries: 100%|██████████| 100/100 [25:16<00:00, 15.17s/it]
07/24/2025 16:45:27 - INFO - __main__ - Computing ROUGE scores...
07/24/2025 16:45:27 - INFO - absl - Using default tokenizer.
07/24/2025 16:45:28 - INFO - __main__ - ROUGE Scores:
07/24/2025 16:45:28 - INFO - __main__ -   rouge1: 0.0602
07/24/2025 16:45:28 - INFO - __main__ -   rouge2: 0.0063
07/24/2025 16:45:28 - INFO - __main__ -   rougeL: 0.0519
07/24/2025 16:45:28 - INFO - __main__ -   rougeLsum: 0.0531
07/24/2025 16:45:28 - INFO - wandb_logger - Logged evaluation results for k_center to W&B
07/24/2025 16:45:28 - INFO - __main__ - Results saved to evaluation_results/eval_k_center_10samples_alpaca.json
07/24/2025 16:45:28 - INFO - wandb_logger - Logged comparison summary to W&B
