#!/usr/bin/env python3
"""
Script to run D2Pruning selection using pre-computed importance scores.
"""

import os
import sys
import json
import numpy as np
import torch
from pathlib import Path
from tqdm import tqdm

# Import the necessary modules from the d2pruning package
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
from data import sampling

def main():
    # Configure paths
    importance_scores_path = Path("../selected_samples/importance_scores.npy")
    embedding_path = Path("./cache/embeddings_dialogsum_train_combined_all-mpnet-base-v2.npy")
    output_path = Path("./results/selection_results_d2pruning_10.json")
    
    # Parameters
    num_samples = 10
    seed = 42
    
    print(f"Loading importance scores from {importance_scores_path}")
    if not importance_scores_path.exists():
        print(f"Error: Importance scores file not found at {importance_scores_path}")
        return
        
    importance_scores = np.load(importance_scores_path)
    print(f"Loaded importance scores with shape: {importance_scores.shape}")
    
    print(f"Loading embeddings from {embedding_path}")
    if not embedding_path.exists():
        print(f"Error: Embeddings file not found at {embedding_path}")
        print("Checking for alternative embeddings...")
        
        # Try to find any embedding file in the cache directory
        cache_dir = Path("./cache")
        if cache_dir.exists():
            embedding_files = list(cache_dir.glob("*embeddings*.npy"))
            if embedding_files:
                embedding_path = embedding_files[0]
                print(f"Found alternative embedding file: {embedding_path}")
            else:
                print("No embedding files found in cache directory.")
                return
        else:
            print("Cache directory not found.")
            return
    
    embeddings = np.load(embedding_path)
    print(f"Loaded embeddings with shape: {embeddings.shape}")
    
    # Check if sizes match
    if importance_scores.shape[0] != embeddings.shape[0]:
        print(f"WARNING: Size mismatch - importance scores ({importance_scores.shape[0]}) vs. embeddings ({embeddings.shape[0]})")
        # Truncate to minimum size
        min_size = min(importance_scores.shape[0], embeddings.shape[0])
        importance_scores = importance_scores[:min_size]
        embeddings = embeddings[:min_size]
        print(f"Truncated both to size {min_size}")
    
    # Normalize scores
    print("Normalizing importance scores...")
    importance_scores = (importance_scores - importance_scores.min()) / (importance_scores.max() - importance_scores.min() + 1e-8)
    
    # D2Pruning requires a dummy 'args' object for its sampler
    class D2Args:
        graph_mode = "product"
        graph_sampling_mode = "absolute"
        n_neighbor = 10
        precomputed_dists = None
        precomputed_neighbors = None
    
    print("Creating D2Pruning sampler...")
    try:
        sampler = sampling.GraphDensitySampler(
            X=embeddings,
            y=None,
            seed=seed,
            importance_scores=torch.from_numpy(importance_scores).float(),
            args=D2Args()
        )
        
        print(f"Running batch selection for {num_samples} samples...")
        selected_indices = sampler.select_batch_(N=num_samples)
        print(f"Selected indices: {selected_indices}")
        
        # Save the selected indices to a file
        output_path.parent.mkdir(exist_ok=True)
        results = {
            'selection_method': 'd2pruning',
            'num_samples': num_samples,
            'selected_indices': selected_indices.tolist() if hasattr(selected_indices, 'tolist') else selected_indices,
        }
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2)
            
        print(f"Results saved to {output_path}")
        
        # Also save to the standard location
        selected_indices_path = Path("../selected_samples/d2pruning_10")
        selected_indices_path.mkdir(exist_ok=True)
        
        np.save(selected_indices_path / "selected_indices.npy", 
                np.array(selected_indices.tolist() if hasattr(selected_indices, 'tolist') else selected_indices))
        
        print(f"Selected indices saved to {selected_indices_path / 'selected_indices.npy'}")
        
    except Exception as e:
        print(f"Error during D2Pruning: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
