#!/usr/bin/env python3
"""
Fix and explain the d2pruning method for DialogSum selection.

This script helps diagnose and fix issues with the d2pruning selection method,
which requires pre-computed training dynamics scores.
"""

import os
import sys
import logging
import argparse
import subprocess
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_training_dynamics_status():
    """Check if training dynamics files exist and their status."""
    logger.info("="*60)
    logger.info("CHECKING D2PRUNING TRAINING DYNAMICS STATUS")
    logger.info("="*60)
    
    # Expected locations for training dynamics
    possible_paths = [
        "./experiment_results/selection_results/training_dynamics_scores.npy",
        "./cache/training_dynamics_scores.npy",
        "./training_dynamics_scores.npy",
        "./results/training_dynamics_scores.npy"
    ]
    
    found_files = []
    for path in possible_paths:
        if Path(path).exists():
            file_size = Path(path).stat().st_size / (1024 * 1024)  # MB
            found_files.append((path, file_size))
            logger.info(f"✅ Found: {path} ({file_size:.1f} MB)")
        else:
            logger.info(f"❌ Not found: {path}")
    
    if found_files:
        logger.info(f"\n✅ Training dynamics files found: {len(found_files)}")
        return found_files[0][0]  # Return first found file
    else:
        logger.info(f"\n❌ No training dynamics files found!")
        return None


def generate_training_dynamics():
    """Generate training dynamics scores."""
    logger.info("="*60)
    logger.info("GENERATING TRAINING DYNAMICS SCORES")
    logger.info("="*60)
    
    output_dir = "./experiment_results/selection_results"
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    cmd = [
        sys.executable, "generate_training_dynamics.py",
        "--output_dir", output_dir,
        "--cache_dir", "./cache",
        "--seed", "42"
    ]
    
    logger.info(f"Running command: {' '.join(cmd)}")
    logger.info("This may take several minutes...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("✅ Training dynamics generation completed!")
        logger.info("Output:", result.stdout[-500:] if result.stdout else "No output")
        return True
    except subprocess.CalledProcessError as e:
        logger.error("❌ Training dynamics generation failed!")
        logger.error(f"Error: {e.stderr}")
        return False


def test_d2pruning_selection(num_samples: int = 10):
    """Test the d2pruning selection method."""
    logger.info("="*60)
    logger.info("TESTING D2PRUNING SELECTION")
    logger.info("="*60)
    
    # Check if training dynamics exist
    dynamics_path = check_training_dynamics_status()
    if not dynamics_path:
        logger.error("Training dynamics not found. Cannot test d2pruning.")
        return False
    
    cmd = [
        sys.executable, "dialogsum_selection.py",
        "--selection_method", "d2pruning",
        "--num_samples", str(num_samples),
        "--difficulty_score_method", "training_dynamics",
        "--difficulty_scores_path", dynamics_path,
        "--cache_dir", "./cache",
        "--output_dir", "./results"
    ]
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("✅ D2Pruning selection completed successfully!")
        logger.info("Output:", result.stdout[-500:] if result.stdout else "No output")
        return True
    except subprocess.CalledProcessError as e:
        logger.error("❌ D2Pruning selection failed!")
        logger.error(f"Error: {e.stderr}")
        return False


def explain_d2pruning():
    """Explain what d2pruning is and how it works."""
    logger.info("="*60)
    logger.info("UNDERSTANDING D2PRUNING METHOD")
    logger.info("="*60)
    
    explanation = """
D2Pruning is a data selection method that uses training dynamics to identify
the most valuable training samples. Here's how it works:

1. TRAINING DYNAMICS GENERATION:
   - Train a model on the dataset while tracking training dynamics
   - Record how the model's confidence changes during training
   - Samples with interesting dynamics (e.g., forgettable examples) are valuable

2. SELECTION PROCESS:
   - Use the training dynamics scores to rank samples
   - Select samples that are neither too easy nor too hard
   - Focus on samples that provide the most learning signal

3. WHY IT'S EFFECTIVE:
   - Identifies samples that are most informative for the model
   - Removes redundant or noisy samples
   - Often achieves better performance with fewer samples

4. REQUIREMENTS:
   - Pre-computed training dynamics scores (generated by training a model)
   - More computationally expensive than other methods
   - But often provides the best selection quality
"""
    
    logger.info(explanation)
    
    logger.info("STEPS TO USE D2PRUNING:")
    logger.info("1. Generate training dynamics: python generate_training_dynamics.py")
    logger.info("2. Run selection: python dialogsum_selection.py --selection_method d2pruning")
    logger.info("3. Or use the experiment pipeline: python run_dialogsum_experiment.py")


def main():
    parser = argparse.ArgumentParser(description="Fix and test d2pruning method")
    parser.add_argument("--action", type=str, 
                       choices=["check", "generate", "test", "explain", "fix"],
                       default="check",
                       help="Action to perform")
    parser.add_argument("--num_samples", type=int, default=10,
                       help="Number of samples for testing")
    
    args = parser.parse_args()
    
    if args.action == "check":
        dynamics_path = check_training_dynamics_status()
        if dynamics_path:
            logger.info(f"\n✅ D2Pruning is ready to use!")
            logger.info(f"Training dynamics found at: {dynamics_path}")
        else:
            logger.info(f"\n❌ D2Pruning requires setup. Run with --action generate")
    
    elif args.action == "generate":
        success = generate_training_dynamics()
        if success:
            logger.info("\n✅ Training dynamics generated successfully!")
            logger.info("You can now use d2pruning selection method.")
        else:
            logger.info("\n❌ Failed to generate training dynamics.")
    
    elif args.action == "test":
        success = test_d2pruning_selection(args.num_samples)
        if success:
            logger.info("\n✅ D2Pruning test completed successfully!")
        else:
            logger.info("\n❌ D2Pruning test failed.")
    
    elif args.action == "explain":
        explain_d2pruning()
    
    elif args.action == "fix":
        logger.info("FIXING D2PRUNING SETUP...")
        
        # Step 1: Check status
        dynamics_path = check_training_dynamics_status()
        
        # Step 2: Generate if needed
        if not dynamics_path:
            logger.info("\nGenerating missing training dynamics...")
            if generate_training_dynamics():
                dynamics_path = check_training_dynamics_status()
        
        # Step 3: Test
        if dynamics_path:
            logger.info("\nTesting d2pruning selection...")
            if test_d2pruning_selection(args.num_samples):
                logger.info("\n🎉 D2Pruning is now working correctly!")
            else:
                logger.info("\n❌ D2Pruning test still failing.")
        else:
            logger.info("\n❌ Could not set up d2pruning.")
    
    logger.info("\n" + "="*60)
    logger.info("NEXT STEPS")
    logger.info("="*60)
    logger.info("To use d2pruning in your experiments:")
    logger.info("python run_dialogsum_experiment.py --selection_methods d2pruning")


if __name__ == "__main__":
    main()
