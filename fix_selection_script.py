#!/usr/bin/env python3
"""
Patch for dialogsum_selection.py that fixes the NumPy int64 to Python int co    
    # Now run the original script with a wrapper
    logger.info(f"Running the fixed script with the following arguments:")
    logger.info(f"  selection_method: {args.selection_method}")
    logger.info(f"  num_samples: {args.num_samples}")
    logger.info(f"  output_dir: {args.output_dir}")

    # Using os.system to run the script
    cmd = f"cd $(dirname {original_script_path}) && python {original_script_path.name} --selection_method {args.selection_method} --num_samples {args.num_samples} --output_dir {args.output_dir} --visualize"
    logger.info(f"Running command: {cmd}")
    os.system(cmd)

if __name__ == "__main__":
    fix_selection_script().
This is a targeted fix for the issue where numpy.int64 indices cause errors when used
with the HuggingFace datasets library.
"""

import json
import argparse
import numpy as np
from pathlib import Path
import logging
import sys
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_selection_script():
    parser = argparse.ArgumentParser(description="Fix dialogsum_selection.py")
    parser.add_argument("--selection_method", type=str, default="random",
                        choices=["random", "moderate", "k_center", "diversity", "d2pruning"],
                        help="Selection method to fix")
    parser.add_argument("--num_samples", type=int, default=50, 
                        help="Number of samples to use")
    parser.add_argument("--output_dir", type=str, default="./d2pruning/results",
                        help="Output directory for results")
    args = parser.parse_args()

    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Get the original script path
    original_script_path = Path("./d2pruning/dialogsum_selection.py")
    if not original_script_path.exists():
        logger.error(f"Original script not found at {original_script_path}")
        return
    
    # Make a backup of the original script
    backup_path = original_script_path.with_suffix(".py.bak")
    if not backup_path.exists():
        with open(original_script_path, 'r') as src, open(backup_path, 'w') as dst:
            dst.write(src.read())
        logger.info(f"Created backup of original script at {backup_path}")

    # Read the original script content
    with open(original_script_path, 'r') as f:
        content = f.read()

    # Define a string that is more likely to be unique
    results_lines = "    results = {\n" 
    results_lines += "        'selection_method': args.selection_method,\n"
    results_lines += "        'num_samples': args.num_samples,\n"
    results_lines += "        'selected_indices': selected_indices.tolist(),\n"
    
    fixed_results_lines = "    # Convert numpy.int64 to Python int to avoid issues with the datasets library\n"
    fixed_results_lines += "    selected_indices_int = [int(idx) for idx in selected_indices]\n"
    fixed_results_lines += "    logger.info(f\"Converted numpy.int64 indices to Python int\")\n\n"
    fixed_results_lines += "    results = {\n"
    fixed_results_lines += "        'selection_method': args.selection_method,\n"
    fixed_results_lines += "        'num_samples': args.num_samples,\n"
    fixed_results_lines += "        'selected_indices': selected_indices.tolist(),\n"

    # Also prepare replacement for the indexing part
    index_line = "'selected_dialogues': [train_data['dialogues'][i] for i in selected_indices],"
    fixed_index_line = "'selected_dialogues': [train_data['dialogues'][i] for i in selected_indices_int],"
    
    index_line2 = "'selected_summaries': [train_data['summaries'][i] for i in selected_indices],"
    fixed_index_line2 = "'selected_summaries': [train_data['summaries'][i] for i in selected_indices_int],"
    
    # Save to standard location string
    save_line = "        selected_indices=selected_indices.tolist(),"
    fixed_save_line = "        selected_indices=selected_indices_int,  # Use the converted indices"
    
    # Check if our content exists and do the replacements
    patched_content = content
    if results_lines in content:
        patched_content = content.replace(results_lines, fixed_results_lines)
        logger.info("Fixed results dictionary initialization")
    else:
        logger.warning("Could not find results dictionary to replace")
    
    if index_line in patched_content:
        patched_content = patched_content.replace(index_line, fixed_index_line)
        logger.info("Fixed selected_dialogues indexing")
    else:
        logger.warning("Could not find selected_dialogues indexing to replace")
        
    if index_line2 in patched_content:
        patched_content = patched_content.replace(index_line2, fixed_index_line2)
        logger.info("Fixed selected_summaries indexing")
    else:
        logger.warning("Could not find selected_summaries indexing to replace")
        
    if save_line in patched_content:
        patched_content = patched_content.replace(save_line, fixed_save_line)
        logger.info("Fixed save_selected_samples call")
    else:
        logger.warning("Could not find save_selected_samples call to replace")
    
    # Write patched content back to file
    with open(original_script_path, 'w') as f:
        f.write(patched_content)
    
    # Now run the original script
    logger.info(f"Running the fixed script with the following arguments:")
    logger.info(f"  selection_method: {args.selection_method}")
    logger.info(f"  num_samples: {args.num_samples}")
    logger.info(f"  output_dir: {args.output_dir}")

    # Using os.system to run the script
    cmd = f"cd $(dirname {original_script_path}) && python {original_script_path.name} --selection_method {args.selection_method} --num_samples {args.num_samples} --output_dir {args.output_dir} --visualize"
    logger.info(f"Running command: {cmd}")
    os.system(cmd)

if __name__ == "__main__":
    fix_selection_script()
