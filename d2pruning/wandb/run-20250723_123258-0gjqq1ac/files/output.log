07/23/2025 12:32:59 - INFO - wandb_logger - Initialized W&B run: selection_moderate_10
07/23/2025 12:32:59 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/0gjqq1ac
07/23/2025 12:32:59 - INFO - __main__ - Starting DialogSum data selection pipeline
07/23/2025 12:32:59 - INFO - __main__ - Selection method: moderate
07/23/2025 12:32:59 - INFO - __main__ - Number of samples: 10
07/23/2025 12:32:59 - INFO - __main__ - Embedding model: all-MiniLM-L6-v2
07/23/2025 12:32:59 - INFO - __main__ - Loading DialogSum dataset...
07/23/2025 12:32:59 - INFO - __main__ - Loading cached DialogSum train dataset from cache/dialogsum_train.pkl
07/23/2025 12:32:59 - INFO - __main__ - Loading cached DialogSum test dataset from cache/dialogsum_test.pkl
07/23/2025 12:32:59 - INFO - __main__ - Train dataset size: 12460
07/23/2025 12:32:59 - INFO - __main__ - Test dataset size: 1500
07/23/2025 12:33:00 - INFO - __main__ - Preprocessed 12460 training samples
07/23/2025 12:33:00 - INFO - __main__ - Average dialogue length: 131.0 words
07/23/2025 12:33:00 - INFO - __main__ - Average summary length: 22.9 words
07/23/2025 12:33:00 - INFO - wandb_logger - Logged dataset information to W&B
07/23/2025 12:33:00 - INFO - __main__ - Extracting embeddings...
07/23/2025 12:33:00 - INFO - __main__ - Loading cached embeddings from cache/embeddings_dialogsum_train_combined_all-MiniLM-L6-v2.npy
07/23/2025 12:33:00 - INFO - __main__ - Extracted embeddings shape: (12460, 384)
07/23/2025 12:33:00 - INFO - wandb_logger - Logged embedding information to W&B
07/23/2025 12:33:00 - INFO - __main__ - Performing moderate selection...
07/23/2025 12:33:00 - INFO - __main__ - Selection completed in 0.44 seconds
07/23/2025 12:33:00 - INFO - __main__ - Selected indices: [    0  4604  9961  9125    66  8744 11464  4380 11831  5179]
07/23/2025 12:33:01 - INFO - wandb_logger - Logged selection results for moderate to W&B
