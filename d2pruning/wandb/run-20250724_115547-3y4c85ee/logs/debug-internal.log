{"time":"2025-07-24T11:55:47.597498975+07:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-24T11:55:47.966264927+07:00","level":"INFO","msg":"stream: created new stream","id":"3y4c85ee"}
{"time":"2025-07-24T11:55:47.966343365+07:00","level":"INFO","msg":"stream: started","id":"3y4c85ee"}
{"time":"2025-07-24T11:55:47.966415525+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"3y4c85ee"}
{"time":"2025-07-24T11:55:47.966492189+07:00","level":"INFO","msg":"sender: started","stream_id":"3y4c85ee"}
{"time":"2025-07-24T11:55:47.966537962+07:00","level":"INFO","msg":"handler: started","stream_id":"3y4c85ee"}
{"time":"2025-07-24T11:57:48.41345902+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:58:03.365670723+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:58:18.365926258+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:58:33.365461728+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:58:48.36541478+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:59:03.365123454+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:59:18.365855369+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:59:33.365658406+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:59:48.365640049+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:00:03.36535001+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:00:18.36579184+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:00:33.365653113+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:00:48.366073549+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:03.365442866+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:18.36510068+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:33.36575162+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:01:48.365554267+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:02:03.365769426+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:02:18.365475042+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T12:02:25.797439414+07:00","level":"INFO","msg":"stream: closing","id":"3y4c85ee"}
{"time":"2025-07-24T12:02:26.717317664+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-24T12:02:27.130277996+07:00","level":"INFO","msg":"sender: closed","stream_id":"3y4c85ee"}
{"time":"2025-07-24T12:02:27.130216054+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"3y4c85ee"}
{"time":"2025-07-24T12:02:27.13062058+07:00","level":"INFO","msg":"handler: closed","stream_id":"3y4c85ee"}
{"time":"2025-07-24T12:02:27.130686885+07:00","level":"INFO","msg":"stream: closed","id":"3y4c85ee"}
