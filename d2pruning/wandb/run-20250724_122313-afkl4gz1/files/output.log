07/24/2025 12:23:16 - INFO - wandb_logger - Initialized W&B run: finetune_k_center_10
07/24/2025 12:23:16 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/afkl4gz1
07/24/2025 12:23:16 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 12:23:19 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [01:14<00:00, 37.45s/it]
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484
07/24/2025 12:24:35 - INFO - __main__ - Preparing training dataset...
Map: 100%|██████████| 10/10 [00:00<00:00, 71.99 examples/s]
07/24/2025 12:24:59 - INFO - __main__ - Starting fine-tuning...
07/24/2025 12:24:59 - INFO - wandb_logger - Logged training start for k_center to W&B
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.
100%|██████████| 9/9 [00:21<00:00,  2.34s/it]
{'train_runtime': 21.1315, 'train_samples_per_second': 1.42, 'train_steps_per_second': 0.426, 'train_loss': 1.8609388139512804, 'epoch': 3.0}
07/24/2025 12:25:21 - INFO - wandb_logger - Logged training completion for k_center to W&B
07/24/2025 12:25:22 - INFO - __main__ - Fine-tuning completed in 21.47 seconds
