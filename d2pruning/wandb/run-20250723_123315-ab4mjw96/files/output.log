07/23/2025 12:33:16 - INFO - wandb_logger - Initialized W&B run: selection_k_center_10
07/23/2025 12:33:16 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/ab4mjw96
07/23/2025 12:33:16 - INFO - __main__ - Starting DialogSum data selection pipeline
07/23/2025 12:33:16 - INFO - __main__ - Selection method: k_center
07/23/2025 12:33:16 - INFO - __main__ - Number of samples: 10
07/23/2025 12:33:16 - INFO - __main__ - Embedding model: all-MiniLM-L6-v2
07/23/2025 12:33:16 - INFO - __main__ - Loading DialogSum dataset...
07/23/2025 12:33:16 - INFO - __main__ - Loading cached DialogSum train dataset from cache/dialogsum_train.pkl
07/23/2025 12:33:16 - INFO - __main__ - Loading cached DialogSum test dataset from cache/dialogsum_test.pkl
07/23/2025 12:33:16 - INFO - __main__ - Train dataset size: 12460
07/23/2025 12:33:16 - INFO - __main__ - Test dataset size: 1500
07/23/2025 12:33:17 - INFO - __main__ - Preprocessed 12460 training samples
07/23/2025 12:33:17 - INFO - __main__ - Average dialogue length: 131.0 words
07/23/2025 12:33:17 - INFO - __main__ - Average summary length: 22.9 words
07/23/2025 12:33:17 - INFO - wandb_logger - Logged dataset information to W&B
07/23/2025 12:33:17 - INFO - __main__ - Extracting embeddings...
07/23/2025 12:33:17 - INFO - __main__ - Loading cached embeddings from cache/embeddings_dialogsum_train_combined_all-MiniLM-L6-v2.npy
07/23/2025 12:33:17 - INFO - __main__ - Extracted embeddings shape: (12460, 384)
07/23/2025 12:33:17 - INFO - wandb_logger - Logged embedding information to W&B
07/23/2025 12:33:17 - INFO - __main__ - Performing k_center selection...
07/23/2025 12:33:22 - INFO - __main__ - Selection completed in 5.04 seconds
07/23/2025 12:33:22 - INFO - __main__ - Selected indices: [ 5584  8049  6924  1478  9146 12177  9825  9968  8874  1828]
07/23/2025 12:33:22 - INFO - wandb_logger - Logged selection results for k_center to W&B
