{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 2.0, "eval_steps": 500, "global_step": 6, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.4, "grad_norm": 0.5073545575141907, "learning_rate": 0.0, "loss": 2.051, "step": 1}, {"epoch": 0.8, "grad_norm": 0.47231969237327576, "learning_rate": 2.0000000000000003e-06, "loss": 2.052, "step": 2}, {"epoch": 1.0, "grad_norm": 0.4352986514568329, "learning_rate": 4.000000000000001e-06, "loss": 1.9976, "step": 3}, {"epoch": 1.4, "grad_norm": 0.46920743584632874, "learning_rate": 6e-06, "loss": 2.1044, "step": 4}, {"epoch": 1.8, "grad_norm": 0.43427613377571106, "learning_rate": 8.000000000000001e-06, "loss": 2.0205, "step": 5}, {"epoch": 2.0, "grad_norm": 0.5604689717292786, "learning_rate": 1e-05, "loss": 1.9178, "step": 6}], "logging_steps": 1, "max_steps": 9, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 229565531553792.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}