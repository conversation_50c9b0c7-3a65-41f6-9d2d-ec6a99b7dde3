_wandb:
    value:
        cli_version: 0.21.0
        e:
            hz80394x4pkselfhuhuj8uz8lqfn165t:
                args:
                    - --selection_results
                    - results/selection_results_moderate_10.json
                    - --model_name
                    - meta-llama/Llama-2-7b-hf
                    - --format_type
                    - chat
                    - --num_epochs
                    - "3"
                    - --output_dir
                    - finetuned_models
                    - --cache_dir
                    - cache
                    - --enable_wandb
                    - --wandb_run_name
                    - finetune_moderate_10
                codePath: finetune_llama.py
                codePathLocal: finetune_llama.py
                cpu_count: 6
                cpu_count_logical: 6
                cudaVersion: "12.9"
                disk:
                    /:
                        total: "982820896768"
                        used: "558405742592"
                email: <EMAIL>
                executable: /storage/nammt/data_selection_for_assistant_model/venv/bin/python
                git:
                    commit: bf1ea46b22cc79549df421fcd92c9cd2d3408008
                    remote: https://github.com/adymaharana/d2pruning.git
                gpu: NVIDIA RTX A4000
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6144
                      memoryTotal: "17171480576"
                      name: NVIDIA RTX A4000
                      uuid: GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a
                host: csews-Precision-7920-Tower
                memory:
                    total: "33291022336"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py
                python: CPython 3.10.12
                root: /storage/nammt/data_selection_for_assistant_model/d2pruning
                startedAt: "2025-07-24T12:45:03.168086Z"
                writerId: hz80394x4pkselfhuhuj8uz8lqfn165t
        m: []
        python_version: 3.10.12
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
                - 100
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
                - 100
            "3":
                - 13
                - 15
                - 16
            "4": 3.10.12
            "5": 0.21.0
            "6": 4.53.2
            "10":
                - 20
            "12": 0.21.0
            "13": linux-x86_64
format_type:
    value: chat
learning_rate:
    value: 0.0002
model_name:
    value: meta-llama/Llama-2-7b-hf
num_epochs:
    value: 3
num_samples:
    value: 10
selection_method:
    value: moderate
task:
    value: fine_tuning
