07/24/2025 12:20:36 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 12:20:36 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/uu02ak9a
07/24/2025 12:20:36 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 12:20:39 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [01:11<00:00, 35.78s/it]
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484
07/24/2025 12:21:52 - INFO - __main__ - Preparing training dataset...
Map: 100%|██████████| 10/10 [00:00<00:00, 77.06 examples/s]
07/24/2025 12:22:16 - INFO - __main__ - Starting fine-tuning...
07/24/2025 12:22:16 - INFO - wandb_logger - Logged training start for moderate to W&B
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.
100%|██████████| 9/9 [00:22<00:00,  2.46s/it]
{'train_runtime': 22.1736, 'train_samples_per_second': 1.353, 'train_steps_per_second': 0.406, 'train_loss': 1.6817061106363933, 'epoch': 3.0}
07/24/2025 12:22:38 - INFO - wandb_logger - Logged training completion for moderate to W&B
07/24/2025 12:22:39 - INFO - __main__ - Fine-tuning completed in 22.55 seconds
