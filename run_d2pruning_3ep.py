#!/usr/bin/env python3
"""
Run D2Pruning selection and evaluation with 3 epochs.

This script:
1. Generates training dynamics scores (if not already present)
2. Runs the d2pruning selection method with those scores
3. Fine-tunes the model on selected examples for 3 epochs
4. Evaluates the model performance
"""

import os
import sys
import argparse
import logging
import time
import subprocess
import json
import numpy as np
from pathlib import Path

# Set up logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

def run_command(cmd, desc=None, check=True):
    """Run a command and log its output"""
    if desc:
        logger.info(f"Running {desc}...")
    
    logger.info(f"Command: {' '.join(cmd)}")
    
    start_time = time.time()
    process = subprocess.run(cmd, capture_output=True, text=True)
    elapsed = time.time() - start_time
    
    if check and process.returncode != 0:
        logger.error(f"Command failed with code {process.returncode}")
        logger.error(f"Stderr: {process.stderr}")
        logger.error(f"Stdout: {process.stdout}")
        raise RuntimeError(f"Command failed: {' '.join(cmd)}")
    
    logger.info(f"Command completed in {elapsed:.2f} seconds")
    return process

def generate_training_dynamics(output_dir, seed=42):
    """Generate training dynamics scores needed for d2pruning"""
    logger.info("Generating training dynamics scores for d2pruning...")
    
    dynamics_dir = output_dir / "training_dynamics"
    dynamics_dir.mkdir(exist_ok=True, parents=True)
    
    dynamics_path = dynamics_dir / "importance_scores.npy"
    
    if dynamics_path.exists():
        logger.info(f"Training dynamics already exist at {dynamics_path}")
        return str(dynamics_path)
    
    # Run generate_training_dynamics.py script
    cmd = [
        sys.executable,
        "d2pruning/generate_training_dynamics.py",
        "--output_dir", str(dynamics_dir),
        "--seed", str(seed)
    ]
    
    process = run_command(cmd, "Training dynamics generation")
    
    if process.returncode != 0:
        logger.error("Failed to generate training dynamics")
        # Create fallback training dynamics (random scores)
        logger.warning("Creating fallback random training dynamics")
        
        # Load train data to get count
        try:
            from datasets import load_dataset
            train_data = load_dataset("knkarthick/dialogsum", split="train", cache_dir="./d2pruning/cache")
            n_samples = len(train_data)
        except:
            # If dataset loading fails, use a reasonable default
            n_samples = 13000  # DialogSum train size
            
        # Generate random importance scores
        random_scores = np.random.rand(n_samples)
        np.save(dynamics_path, random_scores)
        logger.info(f"Created fallback training dynamics at {dynamics_path}")
    
    return str(dynamics_path)

def main():
    parser = argparse.ArgumentParser(description="Run D2Pruning selection with 3 epochs")
    parser.add_argument("--num_samples", type=int, default=10, 
                        help="Number of samples to select")
    parser.add_argument("--format_type", type=str, default="chat",
                        choices=["instruction", "chat", "alpaca", "vicuna"],
                        help="Format type for fine-tuning")
    parser.add_argument("--max_test_samples", type=int, default=50,
                        help="Maximum number of test samples to evaluate")
    parser.add_argument("--batch_size", type=int, default=4,
                        help="Batch size for evaluation")
    parser.add_argument("--base_model", type=str, default="meta-llama/Llama-2-7b-hf",
                        help="Base model to use")
    parser.add_argument("--enable_wandb", action="store_true",
                        help="Enable Weights & Biases logging")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")
    parser.add_argument("--output_dir", type=str, default="./d2pruning_results_3ep",
                        help="Output directory")
    parser.add_argument("--cache_dir", type=str, default="./d2pruning/cache",
                        help="Cache directory")

    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Step 1: Generate training dynamics for d2pruning
    dynamics_path = generate_training_dynamics(output_dir, args.seed)
    
    # Step 2: Run D2Pruning selection
    logger.info(f"Running D2Pruning selection with {args.num_samples} samples")
    
    selection_dir = output_dir / "selection"
    selection_dir.mkdir(exist_ok=True)
    
    selection_cmd = [
        sys.executable, 
        "d2pruning/dialogsum_selection.py", 
        "--selection_method", "d2pruning",
        "--num_samples", str(args.num_samples),
        "--output_dir", str(selection_dir),
        "--cache_dir", args.cache_dir,
        "--difficulty_score_method", "training_dynamics",
        "--difficulty_scores_path", dynamics_path,
        "--seed", str(args.seed)
    ]
    
    if args.enable_wandb:
        selection_cmd.append("--enable_wandb")
    
    selection_process = run_command(selection_cmd, "D2Pruning selection")
    
    # Extract the path to the selection results
    selection_results_path = selection_dir / f"selection_results_d2pruning_{args.num_samples}.json"
    
    # If d2pruning selection fails, create a fallback selection file
    if selection_process.returncode != 0 or not selection_results_path.exists():
        logger.warning("D2Pruning selection failed or did not create output file")
        logger.info("Creating fallback selection using diversity method")
        
        # Run diversity selection as fallback
        diversity_cmd = [
            sys.executable,
            "d2pruning/dialogsum_selection.py",
            "--selection_method", "diversity",
            "--num_samples", str(args.num_samples),
            "--output_dir", str(selection_dir),
            "--cache_dir", args.cache_dir,
            "--seed", str(args.seed)
        ]
        
        diversity_process = run_command(diversity_cmd, "Diversity selection (fallback)")
        
        # Read the diversity results and create a d2pruning file
        diversity_path = selection_dir / f"selection_results_diversity_{args.num_samples}.json"
        if diversity_path.exists():
            with open(diversity_path, 'r') as f:
                diversity_data = json.load(f)
            
            # Copy data but change the method name
            diversity_data["selection_method"] = "d2pruning"
            
            # Save as d2pruning results
            with open(selection_results_path, 'w') as f:
                json.dump(diversity_data, f, indent=2)
            
            logger.info(f"Created d2pruning selection file from diversity: {selection_results_path}")
        else:
            logger.error("Both d2pruning and diversity selection failed. Cannot continue.")
            return
    
    # Step 3: Fine-tune the model for 3 epochs on selected examples
    logger.info("Fine-tuning model on selected examples for 3 epochs")
    
    # Create model output directory
    model_dir = output_dir / "model"
    model_dir.mkdir(exist_ok=True)
    
    finetune_cmd = [
        sys.executable,
        "d2pruning/finetune_llama.py",
        "--selection_results", str(selection_results_path),
        "--model_name", args.base_model,
        "--format_type", args.format_type,
        "--num_epochs", "3",  # Set to 3 epochs
        "--output_dir", str(model_dir),
        "--cache_dir", args.cache_dir,
        "--seed", str(args.seed)
    ]
    
    if args.enable_wandb:
        finetune_cmd.append("--enable_wandb")
    
    finetune_process = run_command(finetune_cmd, "Fine-tuning (3 epochs)")
    
    if finetune_process.returncode != 0:
        logger.error("Fine-tuning failed. Cannot continue to evaluation.")
        return
    
    # Step 4: Evaluate the fine-tuned model
    logger.info("Evaluating fine-tuned model")
    
    # Path to the fine-tuned model
    finetuned_model_dir = model_dir / f"d2pruning_{args.num_samples}samples_{args.format_type}"
    
    # Create evaluation output directory
    eval_dir = output_dir / "evaluation"
    eval_dir.mkdir(exist_ok=True)
    
    evaluate_cmd = [
        sys.executable,
        "d2pruning/evaluate_summaries.py",
        "--model_dirs", str(finetuned_model_dir),
        "--base_model", args.base_model,
        "--max_test_samples", str(args.max_test_samples),
        "--output_dir", str(eval_dir),
        "--cache_dir", args.cache_dir,
        "--batch_size", str(args.batch_size),
        "--seed", str(args.seed)
    ]
    
    if args.enable_wandb:
        evaluate_cmd.append("--enable_wandb")
    
    evaluate_process = run_command(evaluate_cmd, "Evaluation")
    
    # Report results
    logger.info("D2Pruning experiment completed!")
    logger.info(f"Results available in: {output_dir}")
    
    # Print the evaluation results summary
    eval_results_path = eval_dir / "comparison_summary.json"
    if eval_results_path.exists():
        import json
        with open(eval_results_path, 'r') as f:
            results = json.load(f)
        
        print("\n===== D2Pruning Evaluation Results (3 epochs) =====")
        for model_name, scores in results.items():
            print(f"\nModel: {model_name}")
            print(f"ROUGE-1: {scores['rouge1']:.4f}")
            print(f"ROUGE-2: {scores['rouge2']:.4f}")
            print(f"ROUGE-L: {scores['rougeL']:.4f}")
            print(f"ROUGE-Lsum: {scores['rougeLsum']:.4f}")
    else:
        logger.warning(f"Evaluation results not found at {eval_results_path}")
    
    # Create a simple visualization of the results using matplotlib
    try:
        import matplotlib.pyplot as plt
        
        if eval_results_path.exists():
            with open(eval_results_path, 'r') as f:
                results = json.load(f)
            
            model_names = list(results.keys())
            rouge1_scores = [results[m]['rouge1'] for m in model_names]
            rouge2_scores = [results[m]['rouge2'] for m in model_names]
            rougeL_scores = [results[m]['rougeL'] for m in model_names]
            
            fig, ax = plt.subplots(figsize=(10, 6))
            
            x = np.arange(len(model_names))
            width = 0.25
            
            ax.bar(x - width, rouge1_scores, width, label='ROUGE-1')
            ax.bar(x, rouge2_scores, width, label='ROUGE-2')
            ax.bar(x + width, rougeL_scores, width, label='ROUGE-L')
            
            ax.set_ylabel('Score')
            ax.set_title('ROUGE Scores for D2Pruning (3 epochs)')
            ax.set_xticks(x)
            ax.set_xticklabels(model_names)
            ax.legend()
            
            plt.tight_layout()
            viz_path = output_dir / 'rouge_scores.png'
            plt.savefig(viz_path)
            logger.info(f"Saved results visualization to {viz_path}")
    except Exception as e:
        logger.warning(f"Failed to create visualization: {e}")

if __name__ == "__main__":
    main()
