07/24/2025 22:30:25 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_chat_20250724_223023
07/24/2025 22:30:25 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/id231np3
07/24/2025 22:30:25 - INFO - __main__ - ============================================================
07/24/2025 22:30:25 - INFO - __main__ - STEP 1: DATA SELECTION
07/24/2025 22:30:25 - INFO - __main__ - ============================================================
07/24/2025 22:30:25 - INFO - __main__ - Running data selection with method: random
07/24/2025 22:30:25 - INFO - __main__ - Regenerating selection results with new embedding model: meta-llama/Llama-2-7b-hf
07/24/2025 22:30:25 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python dialogsum_selection.py --num_samples 10 --selection_method random --embedding_model meta-llama/Llama-2-7b-hf --cache_dir cache --output_dir results --enable_wandb --wandb_run_name selection_random_10
