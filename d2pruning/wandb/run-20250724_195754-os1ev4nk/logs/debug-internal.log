{"time":"2025-07-24T19:57:54.082413293+07:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-24T19:57:54.44159064+07:00","level":"INFO","msg":"stream: created new stream","id":"os1ev4nk"}
{"time":"2025-07-24T19:57:54.441655381+07:00","level":"INFO","msg":"stream: started","id":"os1ev4nk"}
{"time":"2025-07-24T19:57:54.441702512+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"os1ev4nk"}
{"time":"2025-07-24T19:57:54.441765624+07:00","level":"INFO","msg":"handler: started","stream_id":"os1ev4nk"}
{"time":"2025-07-24T19:57:54.441897483+07:00","level":"INFO","msg":"sender: started","stream_id":"os1ev4nk"}
{"time":"2025-07-24T19:59:38.789372998+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading artifact run-os1ev4nk-trainingmoderatesummary_table-914082dca7f86e2d90f3ae7d48bc03cb","runtime_seconds":2.6591229480000003},{"desc":"uploading output.log","runtime_seconds":0.698158236,"progress":"8.1KB/8.1KB"},{"desc":"uploading config.yaml","runtime_seconds":0.329274429,"progress":"12.2KB/12.2KB"}],"total_operations":3}}
{"time":"2025-07-24T19:59:41.452883391+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-24T19:59:41.812583266+07:00","level":"INFO","msg":"stream: closing","id":"os1ev4nk"}
{"time":"2025-07-24T19:59:41.812634136+07:00","level":"INFO","msg":"handler: closed","stream_id":"os1ev4nk"}
{"time":"2025-07-24T19:59:41.812658015+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"os1ev4nk"}
{"time":"2025-07-24T19:59:41.812679332+07:00","level":"INFO","msg":"sender: closed","stream_id":"os1ev4nk"}
{"time":"2025-07-24T19:59:41.812824382+07:00","level":"INFO","msg":"stream: closed","id":"os1ev4nk"}
