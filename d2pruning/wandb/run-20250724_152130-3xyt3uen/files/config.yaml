_wandb:
    value:
        cli_version: 0.21.0
        e:
            ueb5tix407va4siju409nbdmu5y74ek9:
                args:
                    - --num_samples
                    - "10"
                    - --selection_method
                    - k_center
                    - --embedding_model
                    - all-MiniLM-L6-v2
                    - --cache_dir
                    - cache
                    - --output_dir
                    - results
                    - --enable_wandb
                    - --wandb_run_name
                    - selection_k_center_10
                codePath: dialogsum_selection.py
                codePathLocal: dialogsum_selection.py
                cpu_count: 6
                cpu_count_logical: 6
                cudaVersion: "12.9"
                disk:
                    /:
                        total: "982820896768"
                        used: "558445109248"
                email: <EMAIL>
                executable: /storage/nammt/data_selection_for_assistant_model/venv/bin/python
                git:
                    commit: bf1ea46b22cc79549df421fcd92c9cd2d3408008
                    remote: https://github.com/adymaharana/d2pruning.git
                gpu: NVIDIA RTX A4000
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6144
                      memoryTotal: "17171480576"
                      name: NVIDIA RTX A4000
                      uuid: GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a
                host: csews-Precision-7920-Tower
                memory:
                    total: "33291022336"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /storage/nammt/data_selection_for_assistant_model/d2pruning/dialogsum_selection.py
                python: CPython 3.10.12
                root: /storage/nammt/data_selection_for_assistant_model/d2pruning
                startedAt: "2025-07-24T08:21:30.892593Z"
                writerId: ueb5tix407va4siju409nbdmu5y74ek9
        m: []
        python_version: 3.10.12
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 75
                - 98
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 75
                - 98
            "3":
                - 2
                - 13
                - 15
                - 16
            "4": 3.10.12
            "5": 0.21.0
            "6": 4.53.2
            "12": 0.21.0
            "13": linux-x86_64
embedding_model:
    value: all-MiniLM-L6-v2
num_samples:
    value: 10
seed:
    value: 42
selection_method:
    value: k_center
task:
    value: data_selection
