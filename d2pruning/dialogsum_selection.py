import os
import sys
import json
import pickle
import time
import logging
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any
from collections import Counter

import torch
import datasets
from datasets import load_dataset, Dataset
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModel, set_seed
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA

# Import utility to save selected samples
from utils_save_selection import save_selected_samples
from sklearn.manifold import TSNE
from tqdm import tqdm

sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
from data import CoresetSelection, sampling

# Set up logging to both file and console
log_dir = Path("../logs")
log_dir.mkdir(exist_ok=True)
log_file = log_dir / f"selection_{time.strftime('%Y%m%d_%H%M%S')}.log"

handlers = [
    logging.StreamHandler(),  # Console handler
    logging.FileHandler(log_file)  # File handler
]

logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
    handlers=handlers
)
logger = logging.getLogger(__name__)
logger.info(f"Logging to {log_file}")


class DialogSumProcessor:
    def __init__(self, cache_dir: str = "./cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
    def load_dialogsum(self, split: str = "train") -> Dataset:
        cache_file = self.cache_dir / f"dialogsum_{split}.pkl"
        
        if cache_file.exists():
            logger.info(f"Loading cached DialogSum {split} dataset from {cache_file}")
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        
        logger.info(f"Loading DialogSum {split} dataset from HuggingFace")
        dataset = load_dataset("knkarthick/dialogsum", split=split)
        
        with open(cache_file, 'wb') as f:
            pickle.dump(dataset, f)
        logger.info(f"Cached DialogSum {split} dataset to {cache_file}")
        
        return dataset
    
    def preprocess_for_selection(self, dataset: Dataset) -> Dict[str, Any]:
        """
        Preprocess DialogSum data for selection algorithms.
        
        Returns:
            Dict containing:
            - dialogues: List of dialogue texts
            - summaries: List of summary texts  
            - combined_texts: List of dialogue + summary combined
            - metadata: Additional information
        """
        dialogues = []
        summaries = []
        combined_texts = []
        
        for example in dataset:
            dialogue = example['dialogue'].strip()
            summary = example['summary'].strip()
            
            combined = f"Dialogue: {dialogue}\nSummary: {summary}"
            
            dialogues.append(dialogue)
            summaries.append(summary)
            combined_texts.append(combined)
        
        return {
            'dialogues': dialogues,
            'summaries': summaries, 
            'combined_texts': combined_texts,
            'metadata': {
                'total_samples': len(dialogues),
                'avg_dialogue_length': np.mean([len(d.split()) for d in dialogues]),
                'avg_summary_length': np.mean([len(s.split()) for s in summaries])
            }
        }


class EmbeddingExtractor:
    
    def __init__(self, model_name: str = "meta-llama/Llama-2-7b-hf", cache_dir: str = "./cache"):
        self.model_name = model_name
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.model = None
        self.tokenizer = None
        self.use_llama = "llama" in model_name.lower()

    def _load_model(self):
        if self.model is None:
            if self.use_llama:
                logger.info(f"Loading LLaMA model for embeddings: {self.model_name}")
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModel.from_pretrained(
                    self.model_name,
                    torch_dtype=torch.float16,
                    device_map="auto",
                    load_in_4bit=True
                )
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
                self.model.eval()
            else:
                logger.info(f"Loading sentence transformer model: {self.model_name}")
                self.model = SentenceTransformer(self.model_name)
            
    def extract_embeddings(self, texts: List[str], cache_key: str) -> np.ndarray:
        cache_file = self.cache_dir / f"embeddings_{cache_key}_{self.model_name.replace('/', '_')}.npy"

        if cache_file.exists():
            logger.info(f"Loading cached embeddings from {cache_file}")
            return np.load(cache_file)

        self._load_model()
        logger.info(f"Extracting embeddings for {len(texts)} texts using {self.model_name}")

        if self.use_llama:
            batch_size = 8
            embeddings = []
            for i in tqdm(range(0, len(texts), batch_size), desc=f"Embedding with {self.model_name}"):
                batch_texts = texts[i:i + batch_size]
                batch_embeddings = self._extract_llama_embeddings(batch_texts)
                embeddings.append(batch_embeddings)
            embeddings = np.vstack(embeddings)
        else:
            embeddings = self.model.encode(
                texts,
                batch_size=32,
                show_progress_bar=True,
                convert_to_numpy=True,
                normalize_embeddings=True
            )

        np.save(cache_file, embeddings)
        logger.info(f"Cached embeddings to {cache_file}")

        return embeddings

    def _extract_llama_embeddings(self, texts: List[str]) -> np.ndarray:
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors="pt"
        )

        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}

        with torch.no_grad():
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state

            attention_mask = inputs['attention_mask']
            mask_expanded = attention_mask.unsqueeze(-1).expand(embeddings.size()).float()
            sum_embeddings = torch.sum(embeddings * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            embeddings = sum_embeddings / sum_mask

            embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)

        return embeddings.cpu().numpy()


def create_embedding_based_scores(embeddings: np.ndarray, dialogues: List[str], summaries: List[str]) -> Dict[str, torch.Tensor]:
    num_samples = len(dialogues)

    targets = torch.zeros(num_samples, dtype=torch.long)


    centroid = np.mean(embeddings, axis=0)
    distances = np.linalg.norm(embeddings - centroid, axis=1)
    diversity_scores = torch.from_numpy(distances).float()

    dialogue_lengths = torch.tensor([len(d.split()) for d in dialogues], dtype=torch.float32)
    summary_lengths = torch.tensor([len(s.split()) for s in summaries], dtype=torch.float32)

    diversity_scores = (diversity_scores - diversity_scores.min()) / (diversity_scores.max() - diversity_scores.min() + 1e-8)
    dialogue_lengths = (dialogue_lengths - dialogue_lengths.min()) / (dialogue_lengths.max() - dialogue_lengths.min() + 1e-8)
    summary_lengths = (summary_lengths - summary_lengths.min()) / (summary_lengths.max() - summary_lengths.min() + 1e-8)

    return {
        'targets': targets,
        'confidence': 1.0 - diversity_scores,  
        'entropy': diversity_scores,           
        'dialogue_length': dialogue_lengths,
        'summary_length': summary_lengths,
    }


def k_center_greedy_selection(embeddings: np.ndarray, num_samples: int) -> np.ndarray:
    n_samples = embeddings.shape[0]
    selected_indices = []

    # Start with the sample closest to the centroid
    centroid = np.mean(embeddings, axis=0)
    distances_to_centroid = np.linalg.norm(embeddings - centroid, axis=1)
    first_idx = np.argmin(distances_to_centroid)
    selected_indices.append(first_idx)

    for _ in tqdm(range(num_samples - 1), desc="K-Center Greedy Selection"):
        max_min_distance = -1
        next_idx = -1

        for i in range(n_samples):
            if i in selected_indices:
                continue

            min_distance = float('inf')
            for selected_idx in selected_indices:
                distance = np.linalg.norm(embeddings[i] - embeddings[selected_idx])
                min_distance = min(min_distance, distance)

            if min_distance > max_min_distance:
                max_min_distance = min_distance
                next_idx = i

        if next_idx != -1:
            selected_indices.append(next_idx)

    return np.array(selected_indices)


def visualize_selection(
    embeddings: np.ndarray,
    selected_indices: np.ndarray,
    output_file: Path,
    method: str = 'pca',
    diversity_scores: np.ndarray = None
):
    """
    Generates and saves a 2D visualization of the data selection.
    
    Args:
        embeddings: The embeddings to visualize (n_samples, embedding_dim)
        selected_indices: Indices of selected samples
        output_file: Path to save the visualization
        method: Dimensionality reduction method ('pca' or 'tsne')
        diversity_scores: Optional diversity scores for coloring
    """
    logger.info(f"Generating visualization of selected samples using {method.upper()}...")

    if embeddings is None:
        logger.warning("Cannot generate visualization without embeddings. Skipping.")
        return
    
    # Create directory if it doesn't exist
    output_file.parent.mkdir(exist_ok=True, parents=True)
        
    # Save a copy to the standardized experiment_results/visualizations folder as well
    viz_dir = Path("../experiment_results/visualizations")
    viz_dir.mkdir(exist_ok=True, parents=True)

    # To avoid crashing on small sample sizes for t-SNE
    perplexity = min(30, embeddings.shape[0] - 1)
    if perplexity <= 0:
        logger.warning("Not enough samples to run t-SNE. Skipping visualization.")
        return

    try:
        # Dimensionality Reduction
        if method == 'tsne':
            from sklearn.manifold import TSNE
            reducer = TSNE(n_components=2, random_state=42, perplexity=perplexity)
        else:  # PCA is default
            from sklearn.decomposition import PCA
            reducer = PCA(n_components=2, random_state=42)
    except Exception as e:
        logger.error(f"Error initializing dimensionality reduction: {e}")
        return

    reduced_embeddings = reducer.fit_transform(embeddings)

    plt.figure(figsize=(14, 10))

    # Plot all points
    scatter = plt.scatter(
        reduced_embeddings[:, 0],
        reduced_embeddings[:, 1],
        c=diversity_scores if diversity_scores is not None else '#B0C4DE',  # LightSteelBlue
        cmap='viridis' if diversity_scores is not None else None,
        alpha=0.6,
        s=20,
        label="All Samples"
    )

    # Highlight selected points
    plt.scatter(
        reduced_embeddings[selected_indices, 0],
        reduced_embeddings[selected_indices, 1],
        c='red',
        marker='*',
        s=250,
        edgecolors='black',
        linewidth=1,
        label=f"Selected Samples ({len(selected_indices)})"
    )

    if diversity_scores is not None:
        cbar = plt.colorbar(scatter)
        cbar.set_label('Diversity Score (Higher is more unique)')

    plt.title(f"Data Selection Visualization ({method.upper()})", fontsize=16)
    plt.xlabel(f"{method.upper()} Component 1", fontsize=12)
    plt.ylabel(f"{method.upper()} Component 2", fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.6)
    
    try:
        plt.savefig(output_file, bbox_inches='tight')
        logger.info(f"Saved visualization to {output_file}")
        
        # Save a copy to the standardized location
        viz_filename = output_file.name
        standard_viz_path = Path("../experiment_results/visualizations") / viz_filename
        plt.savefig(standard_viz_path, bbox_inches='tight')
        logger.info(f"Saved copy of visualization to {standard_viz_path}")
        
    except Exception as e:
        logger.error(f"Error saving visualization: {e}")
    finally:
        plt.close()


def main():
    parser = argparse.ArgumentParser(description="DialogSum Statistical Data Selection")
    parser.add_argument("--num_samples", type=int, default=10, 
                       help="Number of samples to select")
    parser.add_argument("--selection_method", type=str, default="moderate",
                       choices=["random", "moderate", "k_center", "diversity", "d2pruning"],
                       help="Selection method to use")
    parser.add_argument("--embedding_model", type=str, default="all-mpnet-base-v2",
                       help="Model for embeddings (e.g., all-mpnet-base-v2 or meta-llama/Llama-2-7b-hf)")
    parser.add_argument("--difficulty_score_method", type=str, default="perplexity",
                        choices=["perplexity", "length", "training_dynamics"],
                        help="Method for calculating difficulty scores for d2pruning")
    parser.add_argument("--difficulty_scores_path", type=str, default=None,
                        help="Path to pre-computed difficulty scores (for training_dynamics)")
    parser.add_argument("--cache_dir", type=str, default="./cache",
                       help="Directory for caching")
    parser.add_argument("--output_dir", type=str, default="./results",
                       help="Output directory for results")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    parser.add_argument("--visualize", action="store_true",
                       help="Enable visualization of the selection")
    parser.add_argument("--viz_method", type=str, default="pca", choices=["pca", "tsne"],
                       help="Method for dimensionality reduction for visualization")
    parser.add_argument("--enable_wandb", action="store_true", default=False,
                       help="Enable Weights & Biases logging")
    parser.add_argument("--wandb_project", type=str, default="data-selection-experiments",
                       help="W&B project name")
    parser.add_argument("--wandb_run_group", type=str, default=None,
                       help="W&B run group name")
    parser.add_argument("--wandb_run_name", type=str, default=None,
                       help="W&B run name (optional)")

    args = parser.parse_args()
    
    set_seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    wandb_logger = None
    if args.enable_wandb:
        try:
            import sys
            sys.path.append(os.path.dirname(__file__))  # Add current directory to path
            from wandb_logger import setup_wandb_logging
        except ImportError:
            logger.error("wandb_logger.py not found. Please ensure it is in the project directory.")
            setup_wandb_logging = None

        if setup_wandb_logging:
            wandb_run_name = args.wandb_run_name or f"selection_{args.selection_method}_{args.num_samples}"
            wandb_config = {
                **vars(args),
                "run_group": args.wandb_run_group,
                "phase": "data_selection"
            }
            
            # Setup wandb logging
            wandb_logger = setup_wandb_logging(
                config=wandb_config,
                experiment_name=wandb_run_name
            )
            
            if wandb_logger.run is None:
                logger.warning("Failed to initialize W&B. Continuing without W&B logging.")
                wandb_logger = None
            else:
                logger.info(f"W&B initialized. Run URL: {wandb_logger.run.url}")

    logger.info("Starting DialogSum data selection pipeline")
    logger.info(f"Selection method: {args.selection_method}")
    logger.info(f"Number of samples: {args.num_samples}")
    logger.info(f"Embedding model: {args.embedding_model}")
    
    processor = DialogSumProcessor(cache_dir=args.cache_dir)
    
    logger.info("Loading DialogSum dataset...")
    train_dataset = processor.load_dialogsum("train")
    test_dataset = processor.load_dialogsum("test")
    
    logger.info(f"Train dataset size: {len(train_dataset)}")
    logger.info(f"Test dataset size: {len(test_dataset)}")
    
    train_data = processor.preprocess_for_selection(train_dataset)
    logger.info(f"Preprocessed {train_data['metadata']['total_samples']} training samples")
    logger.info(f"Average dialogue length: {train_data['metadata']['avg_dialogue_length']:.1f} words")
    logger.info(f"Average summary length: {train_data['metadata']['avg_summary_length']:.1f} words")

    if wandb_logger:
        wandb_logger.log_dataset_info(
            train_size=len(train_dataset),
            test_size=len(test_dataset),
            avg_dialogue_length=train_data['metadata']['avg_dialogue_length'],
            avg_summary_length=train_data['metadata']['avg_summary_length']
        )

    # Conditionally extract embeddings only when needed
    embeddings = None
    selection_requires_embeddings = args.selection_method != "random"

    if selection_requires_embeddings:
        logger.info("Extracting embeddings for selection...")
        extractor = EmbeddingExtractor(model_name=args.embedding_model, cache_dir=args.cache_dir)
        embedding_start_time = time.time()
        embeddings = extractor.extract_embeddings(
            train_data['combined_texts'],
            cache_key="dialogsum_train_combined"
        )
        embedding_time = time.time() - embedding_start_time
        logger.info(f"Extracted embeddings shape: {embeddings.shape}")

        if wandb_logger:
            wandb_logger.log_embedding_info(
                embedding_shape=embeddings.shape,
                embedding_model=args.embedding_model,
                extraction_time=embedding_time
            )
    else:
        logger.info("Skipping embedding extraction for random selection.")
    
    logger.info(f"Performing {args.selection_method} selection...")
    start_time = time.time()

    if args.selection_method == "random":
        selected_indices = CoresetSelection.random_selection(
            total_num=len(train_data['dialogues']),
            num=args.num_samples
        ).numpy()
    elif args.selection_method == "d2pruning":
        if not selection_requires_embeddings:
            raise ValueError("D2Pruning requires embeddings, but they were not generated.")
        
        logger.info(f"Using D2Pruning with difficulty scores from: {args.difficulty_score_method}")
        
        # STRICT IMPLEMENTATION: No fallback.
        # D2Pruning must use pre-computed training dynamics as per the pipeline design.
        if args.difficulty_score_method != "training_dynamics":
            raise ValueError(
                f"Invalid difficulty_score_method for d2pruning: '{args.difficulty_score_method}'. "
                "This pipeline requires 'training_dynamics'."
            )

        if not args.difficulty_scores_path or not Path(args.difficulty_scores_path).exists():
            raise FileNotFoundError(
                "Training dynamics scores file not found or not provided. "
                "Please ensure generate_training_dynamics.py has run successfully and provide the correct "
                "path using --difficulty_scores_path."
            )
        
        logger.info(f"Loading training dynamics from {args.difficulty_scores_path}")
        try:
            difficulty_scores = np.load(args.difficulty_scores_path)
            logger.info(f"Loaded difficulty scores with shape: {difficulty_scores.shape}")
            logger.info(f"Embeddings shape: {embeddings.shape}")
            
            # Check if sizes match
            if difficulty_scores.shape[0] != embeddings.shape[0]:
                logger.warning(f"WARNING: Size mismatch - difficulty scores ({difficulty_scores.shape[0]}) vs. embeddings ({embeddings.shape[0]})")
                # Truncate to minimum size
                min_size = min(difficulty_scores.shape[0], embeddings.shape[0])
                difficulty_scores = difficulty_scores[:min_size]
                embeddings = embeddings[:min_size]
                logger.info(f"Truncated both to size {min_size}")
            
            # Normalize scores
            logger.info("Normalizing difficulty scores...")
            difficulty_scores = (difficulty_scores - difficulty_scores.min()) / \
                                (difficulty_scores.max() - difficulty_scores.min() + 1e-8)
            
            logger.info("Setting up D2Pruning sampler...")
            # D2Pruning requires a dummy 'args' object for its sampler
            class D2Args:
                graph_mode = "product"
                graph_sampling_mode = "absolute"
                n_neighbor = 10
                precomputed_dists = None
                precomputed_neighbors = None
            
            logger.info("Creating GraphDensitySampler...")
            sampler = sampling.GraphDensitySampler(
                X=embeddings,
                y=None,
                seed=args.seed,
                importance_scores=torch.from_numpy(difficulty_scores).float(),
                args=D2Args()
            )
            logger.info(f"Running batch selection for {args.num_samples} samples...")
            selected_indices = sampler.select_batch_(N=args.num_samples)
            logger.info(f"D2Pruning selection complete, got {len(selected_indices)} indices")
        except Exception as e:
            logger.error(f"Error during D2Pruning: {e}", exc_info=True)
            raise

    elif args.selection_method == "moderate":
        if not selection_requires_embeddings:
            raise ValueError("Moderate selection requires embeddings, but they were not generated.")
        embedding_scores = create_embedding_based_scores(
            embeddings, train_data['dialogues'], train_data['summaries']
        )
        ratio = args.num_samples / len(train_data['dialogues'])

        selected_indices = CoresetSelection.moderate_selection(
            data_score=embedding_scores,
            ratio=ratio,
            features=embeddings
        )
    elif args.selection_method == "k_center":
        if not selection_requires_embeddings:
            raise ValueError("K-center selection requires embeddings, but they were not generated.")
        selected_indices = k_center_greedy_selection(embeddings, args.num_samples)
    elif args.selection_method == "diversity":
        if not selection_requires_embeddings:
            raise ValueError("Diversity selection requires embeddings, but they were not generated.")
        embedding_scores = create_embedding_based_scores(
            embeddings, train_data['dialogues'], train_data['summaries']
        )
        diversity_scores = embedding_scores['entropy']
        selected_indices = torch.argsort(diversity_scores, descending=True)[:args.num_samples].numpy()

    selection_time = time.time() - start_time
    logger.info(f"Selection completed in {selection_time:.2f} seconds")
    logger.info(f"Selected indices: {selected_indices}")

    diversity_scores = None
    if args.selection_method in ["moderate", "diversity", "d2pruning"]:
        if embeddings is not None:
            embedding_scores = create_embedding_based_scores(
                embeddings, train_data['dialogues'], train_data['summaries']
            )
            diversity_scores = embedding_scores['entropy'].numpy()

    if wandb_logger:
        wandb_logger.log_selection_results(
            selection_method=args.selection_method,
            selected_indices=selected_indices_int,  # Use the converted indices
            selection_time=selection_time,
            total_samples=len(train_data['dialogues']),
            diversity_scores=diversity_scores
        )

    # Generate and log visualization if requested
    if args.visualize:
        if selection_requires_embeddings:
            viz_file = output_dir / f"selection_viz_{args.selection_method}_{args.num_samples}_{args.viz_method}.png"
            visualize_selection(
                embeddings=embeddings,
                selected_indices=selected_indices,
                output_file=viz_file,
                method=args.viz_method,
                diversity_scores=diversity_scores
            )
            if wandb_logger:
                wandb_logger.log_image(viz_file, caption=f"Selection Visualization: {args.selection_method}")
        else:
            logger.info("Visualization skipped as no embeddings were generated for random selection.")

    # Convert numpy.int64 to Python int to avoid issues with the datasets library
    selected_indices_int = [int(idx) for idx in selected_indices]
    logger.info(f"Converted numpy.int64 indices to Python int")
    
    # Convert numpy.int64 to Python int to avoid issues with the datasets library
    selected_indices_int = [int(idx) for idx in selected_indices]
    logger.info(f"Converted numpy.int64 indices to Python int")

    results = {
        'selection_method': args.selection_method,
        'num_samples': args.num_samples,
        'selected_indices': selected_indices.tolist(),
        'selection_time': selection_time,
        'embedding_model': args.embedding_model,
        'train_dataset_size': len(train_data['dialogues']),
        'selected_dialogues': [train_data['dialogues'][i] for i in selected_indices_int],
        'selected_summaries': [train_data['summaries'][i] for i in selected_indices_int],
        'metadata': train_data['metadata']
    }

    # Save results to output directory
    results_file = output_dir / f"selection_results_{args.selection_method}_{args.num_samples}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
        
    # Also save to a standardized location for easier access across scripts
    samples_with_scores = {
        'dialogues': train_data['dialogues'],
        'summaries': train_data['summaries'],
        'metadata': train_data['metadata']
    }
    
    # Add importance scores if available
    if diversity_scores is not None:
        samples_with_scores['importance_scores'] = diversity_scores
        
    # Save to standard location - using Python int indices
    save_path = save_selected_samples(
        selected_indices=selected_indices_int,  # Use the converted indices
        samples=samples_with_scores,
        selection_method=args.selection_method,
        num_samples=args.num_samples,
        output_dir="../selected_samples"  # This is relative to the d2pruning directory
    )
    
    logger.info(f"Saved standardized selection results to {save_path}")

    if wandb_logger:
        wandb_logger.finish()

    logger.info(f"Results saved to {results_file}")
    logger.info("DialogSum data selection completed successfully!")
