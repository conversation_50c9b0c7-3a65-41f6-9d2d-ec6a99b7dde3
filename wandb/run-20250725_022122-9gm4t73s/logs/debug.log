2025-07-25 02:21:22,713 INFO    MainThread:2995815 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_022122-9gm4t73s/logs/debug.log
2025-07-25 02:21:22,713 INFO    MainThread:2995815 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_022122-9gm4t73s/logs/debug-internal.log
2025-07-25 02:21:22,713 INFO    MainThread:2995815 [wandb_init.py:init():830] calling init triggers
2025-07-25 02:21:22,713 INFO    MainThread:2995815 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_dirs': ['d2pruning/finetuned_models/diversity_10samples_chat', 'd2pruning/finetuned_models/k_center_10samples_chat', 'd2pruning/finetuned_models/moderate_10samples_chat', 'd2pruning/finetuned_models/random_10samples_chat'], 'base_model': 'meta-llama/Llama-2-7b-hf', 'max_test_samples': 5, 'output_dir': './evaluation_results', 'cache_dir': './cache', 'seed': 42, 'batch_size': 4, 'enable_wandb': True, 'wandb_project': 'data-selection-experiments', 'wandb_run_group': None, 'wandb_api_key': '****************************************', '_wandb': {}}
2025-07-25 02:21:22,713 INFO    MainThread:2995815 [wandb_init.py:init():871] starting backend
2025-07-25 02:21:22,713 INFO    MainThread:2995815 [wandb_init.py:init():874] sending inform_init request
2025-07-25 02:21:22,715 INFO    MainThread:2995815 [wandb_init.py:init():882] backend started and connected
2025-07-25 02:21:22,718 INFO    MainThread:2995815 [wandb_init.py:init():953] updated telemetry
2025-07-25 02:21:22,719 INFO    MainThread:2995815 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-25 02:21:23,674 INFO    MainThread:2995815 [wandb_init.py:init():1029] starting run threads in backend
2025-07-25 02:21:23,961 INFO    MainThread:2995815 [wandb_run.py:_console_start():2458] atexit reg
2025-07-25 02:21:23,961 INFO    MainThread:2995815 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-25 02:21:23,961 INFO    MainThread:2995815 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-25 02:21:23,961 INFO    MainThread:2995815 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-25 02:21:23,963 INFO    MainThread:2995815 [wandb_init.py:init():1075] run started, returning control to user process
