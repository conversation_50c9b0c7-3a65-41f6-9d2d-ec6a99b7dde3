_wandb:
    value:
        cli_version: 0.21.0
        e:
            4n8b4fjo7o81gdr9dpdw0uxbnxpewalx:
                args:
                    - --model_dirs
                    - d2pruning/finetuned_models/d2pruning_10samples_chat
                    - d2pruning/finetuned_models/diversity_10samples_chat
                    - d2pruning/finetuned_models/k_center_10samples_chat
                    - d2pruning/finetuned_models/moderate_10samples_chat
                    - d2pruning/finetuned_models/random_10samples_chat
                    - --base_model
                    - meta-llama/Llama-2-7b-hf
                    - --max_test_samples
                    - "500"
                    - --seed
                    - "42"
                    - --enable_wandb
                codePath: d2pruning/evaluate_summaries.py
                codePathLocal: d2pruning/evaluate_summaries.py
                cpu_count: 6
                cpu_count_logical: 6
                cudaVersion: "12.9"
                disk:
                    /:
                        total: "************"
                        used: "************"
                email: <EMAIL>
                executable: /storage/nammt/data_selection_for_assistant_model/venv/bin/python
                gpu: NVIDIA RTX A4000
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6144
                      memoryTotal: "17171480576"
                      name: NVIDIA RTX A4000
                      uuid: GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a
                host: csews-Precision-7920-Tower
                memory:
                    total: "33291022336"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py
                python: CPython 3.10.12
                root: /storage/nammt/data_selection_for_assistant_model
                startedAt: "2025-07-25T09:30:54.631492Z"
                writerId: 4n8b4fjo7o81gdr9dpdw0uxbnxpewalx
        m: []
        python_version: 3.10.12
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
                - 100
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
                - 100
            "3":
                - 2
                - 13
                - 16
            "4": 3.10.12
            "5": 0.21.0
            "6": 4.53.2
            "10":
                - 20
            "12": 0.21.0
            "13": linux-x86_64
base_model:
    value: meta-llama/Llama-2-7b-hf
batch_size:
    value: 4
cache_dir:
    value: ./cache
enable_wandb:
    value: true
max_test_samples:
    value: 500
model_dirs:
    value:
        - d2pruning/finetuned_models/d2pruning_10samples_chat
        - d2pruning/finetuned_models/diversity_10samples_chat
        - d2pruning/finetuned_models/k_center_10samples_chat
        - d2pruning/finetuned_models/moderate_10samples_chat
        - d2pruning/finetuned_models/random_10samples_chat
output_dir:
    value: ./evaluation_results
seed:
    value: 42
wandb_api_key:
    value: ****************************************
wandb_project:
    value: data-selection-experiments
wandb_run_group:
    value: null
