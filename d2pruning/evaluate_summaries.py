#!/usr/bin/env python3
"""
Evaluation Pipeline for DialogSum Summarization

This script evaluates fine-tuned LLaMA models on DialogSum test set using ROUGE metrics.
Revamped to use the preferred evaluation format with proper wandb integration.
"""

import os
import sys
import json
import time
import logging
import argparse
import random
import socket
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from itertools import chain

import torch
from datasets import load_dataset, load_from_disk
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoModelForSeq2SeqLM, set_seed
from torch.utils.data import DataLoader
from peft import PeftModel
from tqdm import tqdm

# Add paths for imports (if needed for score module)
try:
    sys.path.append("/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation")
    sys.path.append(f"{os.getenv('HOME')}/Multi-Level-OT/llm_distillation")
    sys.path.append(f"{os.getenv('HOME')}/Multi-Level-OT")
    import score
    HAS_SCORE_MODULE = True
except ImportError:
    # Fallback to evaluate library
    import evaluate
    HAS_SCORE_MODULE = False

# Set environment variables
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Set up logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


def get_device():
    """Get the best available device."""
    device = "cpu"
    if torch.cuda.is_available():
        device = torch.device("cuda")
    if torch.backends.mps.is_available() and torch.backends.mps.is_built():
        device = torch.device("mps")
    return device


def set_seed_for_reproducibility(seed: int):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    set_seed(seed)
    logger.info(f"Random seed set to {seed} for reproducibility")


def tokenization(items, tokenizer):
    """Tokenize items for batch processing."""
    return tokenizer(items["prompt"], padding='longest', truncation=True, max_length=512)


def create_dialogsum_prompt(dialogue: str, task_type: str = "summary_dialogue") -> str:
    """Create prompt for DialogSum evaluation - matches your format."""
    if task_type == "summary_dialogue":
        return f"Summarize the following dialogue:\n\n{dialogue}\n\nSummary:"
    else:
        return f"### Instruction:\nSummarize the following dialogue:\n\n### Input:\n{dialogue}\n\n### Response:\n"


class SummarizationEvaluator:
    """Handles evaluation of summarization models using the preferred format."""

    def __init__(self, cache_dir: str = "./cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        # Initialize scoring method
        if HAS_SCORE_MODULE:
            logger.info("Using custom score module for evaluation")
            self.use_score_module = True
        else:
            logger.info("Using evaluate library for ROUGE scores")
            self.rouge_metric = evaluate.load("rouge")
            self.use_score_module = False

        self.wandb_instance = None

    def initialize_wandb(self, wandb_api_key: str, model_id: str, dataset_id: str, config: Dict[str, Any]):
        """Initialize wandb with proper error handling."""
        if not wandb_api_key:
            logger.warning("No wandb API key provided, skipping wandb logging")
            return

        try:
            import wandb
            os.environ["WANDB_API_KEY"] = wandb_api_key

            # Create a unique run name
            run_name = f"{model_id.replace('/', '_')}_{dataset_id.replace('/', '_')}_{int(time.time())}"

            self.wandb_instance = wandb
            self.wandb_instance.init(
                project="dialogsum-benchmark",
                name=run_name,
                config=config,
                tags=["dialogsum", "evaluation", "summarization"],
                notes=f"DialogSum evaluation on host: {socket.gethostname()}",
                reinit=True
            )
            logger.info(f"Initialized wandb run: {run_name}")
            logger.info(f"Wandb URL: {self.wandb_instance.run.url}")

        except Exception as e:
            logger.error(f"Failed to initialize wandb: {e}")
            logger.warning("Continuing without wandb logging")
            self.wandb_instance = None

    def safe_wandb_log(self, metrics: Dict[str, Any]):
        """Safely log metrics to wandb."""
        if self.wandb_instance is None:
            return

        try:
            self.wandb_instance.log(metrics)
        except Exception as e:
            logger.warning(f"Failed to log to wandb: {e}")

    def load_test_data(self, max_samples: int = None, seed: int = 42) -> Tuple[List[str], List[str]]:
        """Load DialogSum test dataset."""
        logger.info("Loading DialogSum test dataset...")
        test_dataset = load_dataset("knkarthick/dialogsum", split="test", cache_dir=self.cache_dir)

        # Limit samples if specified (with seeded shuffle for reproducibility)
        if max_samples and len(test_dataset) > max_samples:
            test_dataset = test_dataset.shuffle(seed=seed)
            test_dataset = test_dataset.select(range(max_samples))
            logger.info(f"Limited dataset to {max_samples} samples (shuffled with seed {seed})")

        dialogues = [example['dialogue'].strip() for example in test_dataset]
        summaries = [example['summary'].strip() for example in test_dataset]

        logger.info(f"Loaded {len(dialogues)} test samples")
        return dialogues, summaries
    
    def load_model_and_tokenizer(self, model_id: str, model_tokenizer: str = None,
                                 use_bfloat16: bool = False, seq2seq: bool = False,
                                 is_finetuned: bool = False, base_model: str = None):
        """Load model and tokenizer using your preferred format."""
        device = get_device()
        logger.info(f"Loading model on device: {device}")

        # Load tokenizer
        tokenizer_id = model_tokenizer if model_tokenizer else (base_model if is_finetuned else model_id)
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_id, cache_dir=self.cache_dir)
        tokenizer.add_special_tokens({"pad_token": tokenizer.eos_token})
        tokenizer.padding_side = 'left'

        # Load model
        model_kwargs = {"cache_dir": self.cache_dir}
        if use_bfloat16 and device.type != "cpu":
            model_kwargs["torch_dtype"] = torch.bfloat16

        if is_finetuned and base_model:
            # Load fine-tuned model with LoRA adapters
            logger.info(f"Loading fine-tuned model from {model_id}")
            base_model_obj = AutoModelForCausalLM.from_pretrained(
                base_model,
                torch_dtype=torch.float16,
                device_map="auto",
                load_in_4bit=True,
                **model_kwargs
            )
            model = PeftModel.from_pretrained(base_model_obj, model_id)
        else:
            # Load regular model
            if seq2seq:
                model = AutoModelForSeq2SeqLM.from_pretrained(model_id, **model_kwargs)
            else:
                model = AutoModelForCausalLM.from_pretrained(model_id, **model_kwargs)
            model = model.to(device)

        model.resize_token_embeddings(len(tokenizer))
        model.config.pad_token_id = tokenizer.pad_token_id
        model.eval()

        logger.info("Model and tokenizer loaded successfully")
        return model, tokenizer, device
    
    def prepare_dataset(self, dialogues: List[str], summaries: List[str],
                       task_type: str = "summary_dialogue"):
        """Prepare dataset for evaluation using your format."""
        logger.info("Preparing dataset for evaluation...")

        # Create dataset with prompts
        data = []
        for dialogue, summary in zip(dialogues, summaries):
            prompt = create_dialogsum_prompt(dialogue, task_type)
            data.append({
                'context': dialogue,
                'answers': summary,
                'prompt': prompt
            })

        # Convert to HuggingFace dataset format
        from datasets import Dataset
        dataset = Dataset.from_list(data)

        logger.info(f"Dataset prepared with {len(dataset)} samples")
        return dataset

    def generate_summaries(self, model, tokenizer, dataset, device,
                          batch_size: int = 4, num_workers: int = 2,
                          seq2seq: bool = False, max_new_tokens: int = 150):
        """Generate summaries using your preferred format."""
        logger.info("Starting summary generation...")

        # Tokenize dataset
        def tokenize_batch(items):
            return tokenization(items, tokenizer)

        tokenized_dataset = dataset.map(tokenize_batch, batched=True, batch_size=batch_size)
        tokenized_dataset.set_format(type="torch", columns=["input_ids", "attention_mask"])

        dataloader = DataLoader(tokenized_dataset, batch_size=batch_size, num_workers=num_workers)

        predictions = []
        step = 0

        with torch.no_grad():
            for batch in tqdm(dataloader, desc="Generating summaries"):
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)

                # Generate summaries
                output = model.generate(
                    input_ids,
                    attention_mask=attention_mask,
                    max_new_tokens=max_new_tokens,
                    do_sample=False,
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id
                )

                # For causal LM, remove input tokens from output
                if not seq2seq:
                    generated_sequences = []
                    for i in range(output.shape[0]):
                        input_length = (input_ids[i] != tokenizer.pad_token_id).sum().item()
                        generated_part = output[i, input_length:]
                        generated_sequences.append(generated_part)

                    # Pad sequences for batch decoding
                    max_length = max(len(seq) for seq in generated_sequences)
                    padded_sequences = []
                    for seq in generated_sequences:
                        if len(seq) < max_length:
                            padding = torch.full((max_length - len(seq),), tokenizer.pad_token_id,
                                               dtype=seq.dtype, device=seq.device)
                            seq = torch.cat([seq, padding])
                        padded_sequences.append(seq.unsqueeze(0))

                    output = torch.cat(padded_sequences, dim=0)

                # Decode generated text
                sentences = tokenizer.batch_decode(output, skip_special_tokens=True)

                # Clean up generated text using your format
                cleaned_sentences = []
                for text in sentences:
                    text = text.strip()

                    # Remove leading newlines
                    if text.startswith('\n'):
                        text = text[1:].strip()

                    # Take first meaningful line
                    lines = text.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and len(line) > 1 and line not in [':', ';', '.', ',', '!', '?']:
                            text = line
                            break
                    else:
                        # If no meaningful line found, take first non-empty line
                        for line in lines:
                            line = line.strip()
                            if line:
                                text = line
                                break
                        else:
                            text = "No summary generated."

                    cleaned_sentences.append(text)

                predictions.extend(cleaned_sentences)
                step += len(cleaned_sentences)

                # Live wandb logging every 10 examples
                if self.wandb_instance and step % 10 == 0:
                    try:
                        # Get partial references for live metrics
                        references = [dataset[i]['answers'] for i in range(min(step, len(dataset)))]
                        partial_predictions = predictions[:len(references)]

                        # Compute partial metrics
                        if self.use_score_module:
                            partial_results = score.rouge(partial_predictions, references)
                            for key in partial_results:
                                partial_results[key] = round(partial_results[key] * 100, 2)
                        else:
                            partial_rouge = self.rouge_metric.compute(
                                predictions=partial_predictions,
                                references=references,
                                use_stemmer=True
                            )
                            partial_results = {
                                "rouge1": round(partial_rouge['rouge1'] * 100, 2),
                                "rouge2": round(partial_rouge['rouge2'] * 100, 2),
                                "rougeL": round(partial_rouge['rougeL'] * 100, 2)
                            }

                        live_metrics = {**partial_results, "step": step, "samples": len(partial_predictions)}
                        self.safe_wandb_log(live_metrics)
                        logger.info(f"Live metrics at step {step}: {partial_results}")

                    except Exception as e:
                        logger.warning(f"Failed to compute live metrics: {e}")

        logger.info(f"Generated {len(predictions)} summaries")
        return predictions

    def compute_metrics(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """Compute evaluation metrics using your preferred format."""
        logger.info("Computing evaluation metrics...")

        # Ensure predictions and references have same length
        min_len = min(len(predictions), len(references))
        predictions = predictions[:min_len]
        references = references[:min_len]

        # Clean predictions and references
        predictions = [pred.strip() if pred.strip() else "No summary generated." for pred in predictions]
        references = [ref.strip() for ref in references]

        if self.use_score_module:
            # Use your custom score module
            results = score.f1_score(predictions, references)
            results['em'] = score.exact_match(predictions, references)
            results['squad'] = (results['f1'] + results['em']) / 2
            results.update(score.rouge(predictions, references))

            # Convert to percentages
            for key in results:
                results[key] = round(results[key] * 100, 2)
        else:
            # Use evaluate library as fallback
            rouge_scores = self.rouge_metric.compute(
                predictions=predictions,
                references=references,
                use_stemmer=True
            )

            results = {
                'rouge1': round(rouge_scores['rouge1'] * 100, 2),
                'rouge2': round(rouge_scores['rouge2'] * 100, 2),
                'rougeL': round(rouge_scores['rougeL'] * 100, 2),
                'rougeLsum': round(rouge_scores['rougeLsum'] * 100, 2)
            }

        results['num_samples'] = len(predictions)

        logger.info("Evaluation metrics computed:")
        for metric, score in results.items():
            if metric != 'num_samples':
                logger.info(f"  {metric}: {score:.2f}")

        return results
    
    def evaluate_model(self, model_path: str, base_model: str, format_type: str,
                      test_dialogues: List[str], test_summaries: List[str],
                      max_samples: int = None, wandb_api_key: str = None,
                      selection_method: str = None, batch_size: int = 4,
                      num_workers: int = 2, seed: int = 42) -> Dict[str, Any]:
        """Evaluate a single model using your preferred format."""

        # Initialize wandb if API key provided
        if wandb_api_key:
            config = {
                "model_path": model_path,
                "base_model": base_model,
                "format_type": format_type,
                "max_samples": max_samples,
                "selection_method": selection_method,
                "batch_size": batch_size,
                "seed": seed
            }
            self.initialize_wandb(wandb_api_key, model_path, "knkarthick/dialogsum", config)

        # Limit test samples if specified
        if max_samples is not None:
            # Use seeded shuffle for reproducibility
            indices = list(range(len(test_dialogues)))
            random.Random(seed).shuffle(indices)
            indices = indices[:max_samples]
            test_dialogues = [test_dialogues[i] for i in indices]
            test_summaries = [test_summaries[i] for i in indices]
            logger.info(f"Limited to {max_samples} samples (shuffled with seed {seed})")

        # Load model and tokenizer
        is_finetuned = os.path.exists(os.path.join(model_path, "adapter_config.json"))
        model, tokenizer, device = self.load_model_and_tokenizer(
            model_path, base_model=base_model, use_bfloat16=True,
            is_finetuned=is_finetuned
        )

        # Prepare dataset
        dataset = self.prepare_dataset(test_dialogues, test_summaries, format_type)

        # Generate summaries
        start_time = time.time()
        generated_summaries = self.generate_summaries(
            model, tokenizer, dataset, device, batch_size, num_workers
        )
        generation_time = time.time() - start_time

        # Compute metrics using your preferred format
        results = self.compute_metrics(generated_summaries, test_summaries)

        # Add generation time
        results['generation_time'] = generation_time

        # Log final results to wandb
        if self.wandb_instance:
            final_metrics = {f"final/{k}": v for k, v in results.items() if isinstance(v, (int, float))}
            self.safe_wandb_log(final_metrics)

        # Clean up GPU memory
        del model
        torch.cuda.empty_cache()

        # Finish wandb
        if self.wandb_instance:
            try:
                self.wandb_instance.finish()
            except Exception as e:
                logger.warning(f"Failed to finish wandb: {e}")

        return {
            'rouge_scores': results,
            'generation_time': generation_time,
            'num_test_samples': len(test_dialogues),
            'generated_summaries': generated_summaries[:5],  # Save first 5 for inspection
            'reference_summaries': test_summaries[:5]
        }


def load_training_results(results_file: str) -> Dict[str, Any]:
    """Load training results."""
    with open(results_file, 'r') as f:
        return json.load(f)


def main():
    parser = argparse.ArgumentParser(description="Evaluate DialogSum Summarization Models")
    parser.add_argument("--model_dirs", type=str, nargs='+', required=True,
                       help="Directories containing fine-tuned models")
    parser.add_argument("--base_model", type=str, default="meta-llama/Llama-2-7b-hf",
                       help="Base LLaMA model name")
    parser.add_argument("--max_test_samples", type=int, default=500,
                       help="Maximum number of test samples to evaluate (default: 500)")
    parser.add_argument("--output_dir", type=str, default="./evaluation_results",
                       help="Output directory for evaluation results")
    parser.add_argument("--cache_dir", type=str, default="./cache",
                       help="Cache directory")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    parser.add_argument("--batch_size", type=int, default=4,
                       help="Batch size for evaluation")
    parser.add_argument("--num_workers", type=int, default=2,
                       help="Number of workers for data loading")
    parser.add_argument("--wandb_api_key", type=str, default=None,
                       help="Wandb API key for logging")
    parser.add_argument("--enable_wandb", action="store_true", default=False,
                       help="Enable Weights & Biases logging")
    parser.add_argument("--wandb_run_name", type=str, default=None,
                       help="W&B run name (optional)")

    args = parser.parse_args()

    # Set random seeds for reproducibility
    set_seed_for_reproducibility(args.seed)

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    # Get wandb API key
    wandb_api_key = args.wandb_api_key
    if args.enable_wandb and not wandb_api_key:
        wandb_api_key = os.getenv("WANDB_API_KEY")
        if not wandb_api_key:
            logger.warning("Wandb enabled but no API key provided. Set WANDB_API_KEY environment variable.")
            wandb_api_key = None

    # Initialize evaluator
    evaluator = SummarizationEvaluator(cache_dir=args.cache_dir)

    # Load test data with 500 samples limit and seed 42
    test_dialogues, test_summaries = evaluator.load_test_data(
        max_samples=args.max_test_samples,
        seed=args.seed
    )

    # Evaluate each model
    all_results = {}

    for model_dir in args.model_dirs:
        model_path = Path(model_dir)
        if not model_path.exists():
            logger.warning(f"Model directory {model_path} does not exist, skipping...")
            continue

        # Load training results to get metadata
        training_results_file = model_path / "training_results.json"
        if not training_results_file.exists():
            logger.warning(f"Training results file not found in {model_path}, skipping...")
            continue

        training_data = load_training_results(training_results_file)
        selection_method = training_data['selection_method']
        format_type = training_data['format_type']
        num_samples = training_data['num_samples']

        logger.info(f"Evaluating model: {selection_method}_{num_samples}samples_{format_type}")

        # Evaluate the model using your preferred format
        eval_results = evaluator.evaluate_model(
            model_path=str(model_path),
            base_model=args.base_model,
            format_type=format_type,
            test_dialogues=test_dialogues,
            test_summaries=test_summaries,
            max_samples=args.max_test_samples,
            wandb_api_key=wandb_api_key,
            selection_method=selection_method,
            batch_size=args.batch_size,
            num_workers=args.num_workers,
            seed=args.seed
        )

        # Combine with training data
        combined_results = {
            **training_data,
            'evaluation_results': eval_results
        }
        
        model_key = f"{selection_method}_{num_samples}samples_{format_type}"
        all_results[model_key] = combined_results
        
        # Save individual results
        individual_results_file = output_dir / f"eval_{model_key}.json"
        with open(individual_results_file, 'w') as f:
            json.dump(combined_results, f, indent=2)
        
        logger.info(f"Results saved to {individual_results_file}")
    
    # Save combined results
    combined_results_file = output_dir / "all_evaluation_results.json"
    with open(combined_results_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    # Create comparison summary
    comparison_summary = {}
    for model_key, results in all_results.items():
        rouge_scores = results['evaluation_results']['rouge_scores']
        training_time = results.get('training_results', {}).get('training_time', 0)
        generation_time = results['evaluation_results']['generation_time']

        comparison_summary[model_key] = {
            'selection_method': results['selection_method'],
            'num_samples': results['num_samples'],
            'rouge1': rouge_scores.get('rouge1', 0),
            'rouge2': rouge_scores.get('rouge2', 0),
            'rougeL': rouge_scores.get('rougeL', 0),
            'rougeLsum': rouge_scores.get('rougeLsum', 0),
            'training_time': training_time,
            'generation_time': generation_time
        }

    summary_file = output_dir / "comparison_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(comparison_summary, f, indent=2)

    logger.info(f"Comparison summary saved to {summary_file}")
    logger.info("Evaluation completed successfully!")

    # Print summary
    logger.info("="*60)
    logger.info("EVALUATION SUMMARY")
    logger.info("="*60)
    for model_key, summary in comparison_summary.items():
        logger.info(f"Model: {model_key}")
        logger.info(f"  ROUGE-1: {summary['rouge1']:.2f}")
        logger.info(f"  ROUGE-2: {summary['rouge2']:.2f}")
        logger.info(f"  ROUGE-L: {summary['rougeL']:.2f}")
        logger.info(f"  Generation Time: {summary['generation_time']:.2f}s")
        logger.info("")
    print("\n" + "="*80)
    print("EVALUATION SUMMARY")
    print("="*80)
    for model_key, summary in comparison_summary.items():
        print(f"\nModel: {model_key}")
        print(f"  Selection Method: {summary['selection_method']}")
        print(f"  Training Samples: {summary['num_samples']}")
        print(f"  ROUGE-1: {summary['rouge1']:.4f}")
        print(f"  ROUGE-2: {summary['rouge2']:.4f}")
        print(f"  ROUGE-L: {summary['rougeL']:.4f}")
        print(f"  Training Time: {summary['training_time']:.2f}s")
        print(f"  Generation Time: {summary['generation_time']:.2f}s")


if __name__ == "__main__":
    main()
