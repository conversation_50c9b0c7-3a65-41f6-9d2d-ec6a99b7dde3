{"time":"2025-07-24T11:35:09.522283639+07:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-24T11:35:09.898174038+07:00","level":"INFO","msg":"stream: created new stream","id":"x09ubqjk"}
{"time":"2025-07-24T11:35:09.898242304+07:00","level":"INFO","msg":"stream: started","id":"x09ubqjk"}
{"time":"2025-07-24T11:35:09.898274017+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"x09ubqjk"}
{"time":"2025-07-24T11:35:09.898364539+07:00","level":"INFO","msg":"handler: started","stream_id":"x09ubqjk"}
{"time":"2025-07-24T11:35:09.898394643+07:00","level":"INFO","msg":"sender: started","stream_id":"x09ubqjk"}
{"time":"2025-07-24T11:46:25.402261194+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:46:40.285622615+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:46:55.285315653+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:47:10.285378582+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:47:25.28498436+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:47:40.285719673+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:47:55.286089565+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:48:10.285870133+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:48:25.285117302+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:48:40.285719696+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:48:55.285516745+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:49:10.286006665+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:49:25.285714488+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:49:40.285419414+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:49:55.285417411+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:50:10.285157389+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:50:25.285859565+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:50:40.285273602+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:50:55.285213585+07:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-07-24T11:51:10.255931972+07:00","level":"INFO","msg":"stream: closing","id":"x09ubqjk"}
{"time":"2025-07-24T11:51:11.784722006+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-24T11:51:12.116145649+07:00","level":"INFO","msg":"handler: closed","stream_id":"x09ubqjk"}
{"time":"2025-07-24T11:51:12.116235389+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"x09ubqjk"}
{"time":"2025-07-24T11:51:12.116247488+07:00","level":"INFO","msg":"sender: closed","stream_id":"x09ubqjk"}
{"time":"2025-07-24T11:51:12.116392981+07:00","level":"INFO","msg":"stream: closed","id":"x09ubqjk"}
