{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 0.3209242618741977, "eval_steps": 500, "global_step": 500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.03209242618741977, "grad_norm": Infinity, "learning_rate": 4.849165596919127e-05, "loss": 0.5574, "step": 50}, {"epoch": 0.06418485237483953, "grad_norm": 3.575394630432129, "learning_rate": 4.69191270860077e-05, "loss": 0.7187, "step": 100}, {"epoch": 0.0962772785622593, "grad_norm": 8.070857048034668, "learning_rate": 4.531450577663672e-05, "loss": 0.7305, "step": 150}, {"epoch": 0.12836970474967907, "grad_norm": 3.668419361114502, "learning_rate": 4.370988446726573e-05, "loss": 0.56, "step": 200}, {"epoch": 0.16046213093709885, "grad_norm": 16.039926528930664, "learning_rate": 4.210526315789474e-05, "loss": 0.5477, "step": 250}, {"epoch": 0.1925545571245186, "grad_norm": 6.599456787109375, "learning_rate": 4.0500641848523754e-05, "loss": 0.5126, "step": 300}, {"epoch": 0.2246469833119384, "grad_norm": 4.171901226043701, "learning_rate": 3.8896020539152764e-05, "loss": 0.4713, "step": 350}, {"epoch": 0.25673940949935814, "grad_norm": 3.7021212577819824, "learning_rate": 3.7291399229781774e-05, "loss": 0.4231, "step": 400}, {"epoch": 0.2888318356867779, "grad_norm": 11.612005233764648, "learning_rate": 3.5686777920410784e-05, "loss": 0.4681, "step": 450}, {"epoch": 0.3209242618741977, "grad_norm": 0.7501663565635681, "learning_rate": 3.4082156611039793e-05, "loss": 0.4302, "step": 500}], "logging_steps": 50, "max_steps": 1558, "num_input_tokens_seen": 0, "num_train_epochs": 1, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 730503178545600.0, "train_batch_size": 8, "trial_name": null, "trial_params": null}