---
extra_gated_heading: You need to share contact information with Meta to access this model
extra_gated_prompt: >-
  ### LLAMA 2 COMMUNITY LICENSE AGREEMENT

  "Agreement" means the terms and conditions for use, reproduction, distribution
  and  modification of the Llama Materials set forth herein. 

  "Documentation" means the specifications, manuals and documentation 
  accompanying Llama 2 distributed by Meta at
  https://ai.meta.com/resources/models-and-libraries/llama-downloads/.  

  "Licensee" or "you" means you, or your employer or any other person or entity
  (if you are entering into this Agreement on such person or entity's behalf),
  of the age required under applicable laws, rules or regulations to provide
  legal consent and that has legal authority to bind your employer or such other
  person or  entity if you are  entering in this Agreement on their behalf. 

  "Llama 2" means the foundational large language models and software and
  algorithms, including machine-learning model code, trained model weights,
  inference-enabling code, training-enabling code, fine-tuning enabling code and
  other  elements of the foregoing distributed by Meta at
  ai.meta.com/resources/models-and-libraries/llama-downloads/.

  "Llama Materials" means, collectively, Meta's proprietary Llama 2 and
  documentation (and any portion thereof) made available under this Agreement.

  "Meta" or "we" means Meta Platforms Ireland Limited (if you are located in or,
  if you are an entity, your principal place of business is in the EEA or
  Switzerland) and Meta Platforms, Inc. (if you are located outside of the EEA
  or Switzerland). 


  By clicking "I Accept" below or by using or distributing any portion or
  element of the Llama Materials, you agree to be bound by this Agreement.

  1. License Rights and Redistribution. 

  a. Grant of Rights. You are granted a non-exclusive, worldwide, non-
  transferable and royalty-free limited license under Meta's intellectual
  property or  other rights owned by Meta embodied in the Llama Materials to
  use, reproduce,  distribute, copy, create derivative works of, and make
  modifications to the Llama  Materials.  

  b. Redistribution and Use.

  i. If you distribute or make the Llama Materials, or any derivative works 
  thereof, available to a third party, you shall provide a copy of this
  Agreement to such  third party. 

  ii.  If you receive Llama Materials, or any derivative works thereof, from  a
  Licensee as part of an integrated end user product, then Section 2 of this 
  Agreement will not apply to you. 

  iii. You must retain in all copies of the Llama Materials that you  distribute
  the following attribution notice within a "Notice" text file distributed as a 
  part of such copies: "Llama 2 is licensed under the LLAMA 2 Community
  License,  Copyright (c) Meta Platforms, Inc. All Rights Reserved."

  iv. Your use of the Llama Materials must comply with applicable laws  and
  regulations (including trade compliance laws and regulations) and adhere to
  the  Acceptable Use Policy for the Llama Materials (available at 
  https://ai.meta.com/llama/use-policy), which is hereby incorporated by
  reference into  this Agreement.

  v. You will not use the Llama Materials or any output or results of the  Llama
  Materials to improve any other large language model (excluding Llama 2 or 
  derivative works thereof).  


  2. Additional Commercial Terms. If, on the Llama 2 version release date, the 
  monthly active users of the products or services made available by or for
  Licensee,  or Licensee's affiliates, is greater than 700 million monthly
  active users in the  preceding calendar month, you must request a license from
  Meta, which Meta may  grant to you in its sole discretion, and you are not
  authorized to exercise any of the  rights under this Agreement unless or until
  Meta otherwise expressly grants you  such rights.

  3. Disclaimer of Warranty. UNLESS REQUIRED BY APPLICABLE LAW, THE  LLAMA
  MATERIALS AND ANY OUTPUT AND RESULTS THEREFROM ARE  PROVIDED ON AN "AS IS"
  BASIS, WITHOUT WARRANTIES OF ANY KIND,  EITHER EXPRESS OR IMPLIED, INCLUDING,
  WITHOUT LIMITATION, ANY  WARRANTIES OF TITLE, NON-INFRINGEMENT,
  MERCHANTABILITY, OR  FITNESS FOR A PARTICULAR PURPOSE. YOU ARE SOLELY
  RESPONSIBLE  FOR DETERMINING THE APPROPRIATENESS OF USING OR REDISTRIBUTING 
  THE LLAMA MATERIALS AND ASSUME ANY RISKS ASSOCIATED WITH YOUR  USE OF THE
  LLAMA MATERIALS AND ANY OUTPUT AND RESULTS.

  4. Limitation of Liability. IN NO EVENT WILL META OR ITS AFFILIATES BE  LIABLE
  UNDER ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, TORT,  NEGLIGENCE,
  PRODUCTS LIABILITY, OR OTHERWISE, ARISING OUT OF THIS  AGREEMENT, FOR ANY LOST
  PROFITS OR ANY INDIRECT, SPECIAL,  CONSEQUENTIAL, INCIDENTAL, EXEMPLARY OR
  PUNITIVE DAMAGES, EVEN  IF META OR ITS AFFILIATES HAVE BEEN ADVISED OF THE
  POSSIBILITY OF  ANY OF THE FOREGOING.


  5. Intellectual Property.

  a. No trademark licenses are granted under this Agreement, and in  connection
  with the Llama Materials, neither Meta nor Licensee may use any name  or mark
  owned by or associated with the other or any of its affiliates, except as 
  required for reasonable and customary use in describing and redistributing
  the  Llama Materials.

  b. Subject to Meta's ownership of Llama Materials and derivatives made by or 
  for Meta, with respect to any derivative works and modifications of the Llama 
  Materials that are made by you, as between you and Meta, you are and will be
  the  owner of such derivative works and modifications.

  c. If you institute litigation or other proceedings against Meta or any
  entity  (including a cross-claim or counterclaim in a lawsuit) alleging that
  the Llama  Materials or Llama 2 outputs or results, or any portion of any of
  the foregoing,  constitutes infringement of intellectual property or other
  rights owned or licensable  by you, then any licenses granted to you under
  this Agreement shall terminate as of  the date such litigation or claim is
  filed or instituted. You will indemnify and hold  harmless Meta from and
  against any claim by any third party arising out of or related  to your use or
  distribution of the Llama Materials.

  6. Term and Termination. The term of this Agreement will commence upon your 
  acceptance of this Agreement or access to the Llama Materials and will
  continue in  full force and effect until terminated in accordance with the
  terms and conditions  herein. Meta may terminate this Agreement if you are in
  breach of any term or  condition of this Agreement. Upon termination of this
  Agreement, you shall delete  and cease use of the Llama Materials. Sections 3,
  4 and 7 shall survive the  termination of this Agreement. 

  7. Governing Law and Jurisdiction. This Agreement will be governed and 
  construed under the laws of the State of California without regard to choice
  of law  principles, and the UN Convention on Contracts for the International
  Sale of Goods  does not apply to this Agreement. The courts of California
  shall have exclusive  jurisdiction of any dispute arising out of this
  Agreement. 

  ### Llama 2 Acceptable Use Policy

  Meta is committed to promoting safe and fair use of its tools and features,
  including Llama 2. If you access or use Llama 2, you agree to this Acceptable
  Use Policy (“Policy”). The most recent copy of this policy can be found at
  [ai.meta.com/llama/use-policy](http://ai.meta.com/llama/use-policy).

  #### Prohibited Uses

  We want everyone to use Llama 2 safely and responsibly. You agree you will not
  use, or allow others to use, Llama 2 to:

  1. Violate the law or others’ rights, including to:
        1. Engage in, promote, generate, contribute to, encourage, plan, incite, or further illegal or unlawful activity or content, such as: 
            1. Violence or terrorism 
            2. Exploitation or harm to children, including the solicitation, creation, acquisition, or dissemination of child exploitative content or failure to report Child Sexual Abuse Material
            3. Human trafficking, exploitation, and sexual violence
            4. The illegal distribution of information or materials to minors, including obscene materials, or failure to employ legally required age-gating in connection with such information or materials.
            5. Sexual solicitation
            6. Any other criminal activity
        2. Engage in, promote, incite, or facilitate the harassment, abuse, threatening, or bullying of individuals or groups of individuals
        3. Engage in, promote, incite, or facilitate discrimination or other unlawful or harmful conduct in the provision of employment, employment benefits, credit, housing, other economic benefits, or other essential goods and services
        4. Engage in the unauthorized or unlicensed practice of any profession including, but not limited to, financial, legal, medical/health, or related professional practices 
        5. Collect, process, disclose, generate, or infer health, demographic, or other sensitive personal or private information about individuals without rights and consents required by applicable laws
        6. Engage in or facilitate any action or generate any content that infringes, misappropriates, or otherwise violates any third-party rights, including the outputs or results of any products or services using the Llama 2 Materials
        7. Create, generate, or facilitate the creation of malicious code, malware, computer viruses or do anything else that could disable, overburden, interfere with or impair the proper working, integrity, operation or appearance of a website or computer system 
  2. Engage in, promote, incite, facilitate, or assist in the planning or
  development of activities that present a risk of death or bodily harm to
  individuals, including use of Llama 2 related to the following:
      1. Military, warfare, nuclear industries or applications, espionage, use for materials or activities that are subject to the International Traffic Arms Regulations (ITAR) maintained by the United States Department of State
      2. Guns and illegal weapons (including weapon development)
      3. Illegal drugs and regulated/controlled substances
      4. Operation of critical infrastructure, transportation technologies, or heavy machinery
      5. Self-harm or harm to others, including suicide, cutting, and eating disorders
      6. Any content intended to incite or promote violence, abuse, or any infliction of bodily harm to an individual
  3. Intentionally deceive or mislead others, including use of Llama 2 related
  to the following:
      1. Generating, promoting, or furthering fraud or the creation or promotion of disinformation
      2. Generating, promoting, or furthering defamatory content, including the creation of defamatory statements, images, or other content
      3. Generating, promoting, or further distributing spam
      4. Impersonating another individual without consent, authorization, or legal right
      5. Representing that the use of Llama 2 or outputs are human-generated
      6. Generating or facilitating false online engagement, including fake reviews and other means of fake online engagement 
      4. Fail to appropriately disclose to end users any known dangers of your AI system 
  Please report any violation of this Policy, software “bug,” or other problems
  that could lead to a violation of this Policy through one of the following
  means: 
      * Reporting issues with the model: [github.com/facebookresearch/llama](http://github.com/facebookresearch/llama)
      * Reporting risky content generated by the model: [developers.facebook.com/llama_output_feedback](http://developers.facebook.com/llama_output_feedback)
      * Reporting bugs and security concerns: [facebook.com/whitehat/info](http://facebook.com/whitehat/info) 
      * Reporting violations of the Acceptable Use Policy or unlicensed uses of Llama: [<EMAIL>](mailto:<EMAIL>)
extra_gated_fields:
  First Name: text
  Last Name: text
  Date of birth: date_picker
  Country: country
  Affiliation: text
  geo: ip_location
  By clicking Submit below I accept the terms of the license and acknowledge that the information I provide will be collected stored processed and shared in accordance with the Meta Privacy Policy: checkbox
extra_gated_description: >-
  The information you provide will be collected, stored, processed and shared in
  accordance with the [Meta Privacy
  Policy](https://www.facebook.com/privacy/policy/).
extra_gated_button_content: Submit
language:
- en
pipeline_tag: text-generation
tags:
- facebook
- meta
- pytorch
- llama
- llama-2
license: llama2
---
# **Llama 2**
Llama 2 is a collection of pretrained and fine-tuned generative text models ranging in scale from 7 billion to 70 billion parameters. This is the repository for the 7B pretrained model, converted for the Hugging Face Transformers format. Links to other models can be found in the index at the bottom.

## Model Details
*Note: Use of this model is governed by the Meta license. In order to download the model weights and tokenizer, please visit the [website](https://ai.meta.com/resources/models-and-libraries/llama-downloads/) and accept our License before requesting access here.*

Meta developed and publicly released the Llama 2 family of large language models (LLMs), a collection of pretrained and fine-tuned generative text models ranging in scale from 7 billion to 70 billion parameters. Our fine-tuned LLMs, called Llama-2-Chat, are optimized for dialogue use cases. Llama-2-Chat models outperform open-source chat models on most benchmarks we tested, and in our human evaluations for helpfulness and safety, are on par with some popular closed-source models like ChatGPT and PaLM.

**Model Developers** Meta

**Variations** Llama 2 comes in a range of parameter sizes — 7B, 13B, and 70B — as well as pretrained and fine-tuned variations.

**Input** Models input text only.

**Output** Models generate text only.

**Model Architecture** Llama 2 is an auto-regressive language model that uses an optimized transformer architecture. The tuned versions use supervised fine-tuning (SFT) and reinforcement learning with human feedback (RLHF) to align to human preferences for helpfulness and safety.


||Training Data|Params|Content Length|GQA|Tokens|LR|
|---|---|---|---|---|---|---|
|Llama 2|*A new mix of publicly available online data*|7B|4k|&#10007;|2.0T|3.0 x 10<sup>-4</sup>|
|Llama 2|*A new mix of publicly available online data*|13B|4k|&#10007;|2.0T|3.0 x 10<sup>-4</sup>|
|Llama 2|*A new mix of publicly available online data*|70B|4k|&#10004;|2.0T|1.5 x 10<sup>-4</sup>|

*Llama 2 family of models.* Token counts refer to pretraining data only. All models are trained with a global batch-size of 4M tokens. Bigger models -  70B -- use Grouped-Query Attention (GQA) for improved inference scalability.

**Model Dates** Llama 2 was trained between January 2023 and July 2023.

**Status** This is a static model trained on an offline dataset. Future versions of the tuned models will be released as we improve model safety with community feedback.

**License** A custom commercial license is available at: [https://ai.meta.com/resources/models-and-libraries/llama-downloads/](https://ai.meta.com/resources/models-and-libraries/llama-downloads/)

**Research Paper** ["Llama-2: Open Foundation and Fine-tuned Chat Models"](arxiv.org/abs/2307.09288)

## Intended Use
**Intended Use Cases** Llama 2 is intended for commercial and research use in English. Tuned models are intended for assistant-like chat, whereas pretrained models can be adapted for a variety of natural language generation tasks.

To get the expected features and performance for the chat versions, a specific formatting needs to be followed, including the `INST` and `<<SYS>>` tags, `BOS` and `EOS` tokens, and the whitespaces and breaklines in between (we recommend calling `strip()` on inputs to avoid double-spaces). See our reference code in github for details: [`chat_completion`](https://github.com/facebookresearch/llama/blob/main/llama/generation.py#L212).

**Out-of-scope Uses** Use in any manner that violates applicable laws or regulations (including trade compliance laws).Use in languages other than English. Use in any other way that is prohibited by the Acceptable Use Policy and Licensing Agreement for Llama 2.

## Hardware and Software
**Training Factors** We used custom training libraries, Meta's Research Super Cluster, and production clusters for pretraining. Fine-tuning, annotation, and evaluation were also performed on third-party cloud compute.

**Carbon Footprint** Pretraining utilized a cumulative 3.3M GPU hours of computation on hardware of type A100-80GB (TDP of 350-400W). Estimated total emissions were 539 tCO2eq, 100% of which were offset by Meta’s sustainability program.

||Time (GPU hours)|Power Consumption (W)|Carbon Emitted(tCO<sub>2</sub>eq)|
|---|---|---|---|
|Llama 2 7B|184320|400|31.22|
|Llama 2 13B|368640|400|62.44|
|Llama 2 70B|1720320|400|291.42|
|Total|3311616||539.00|

**CO<sub>2</sub> emissions during pretraining.** Time: total GPU time required for training each model. Power Consumption: peak power capacity per GPU device for the GPUs used adjusted for power usage efficiency. 100% of the emissions are directly offset by Meta's sustainability program, and because we are openly releasing these models, the pretraining costs do not need to be incurred by others.

## Training Data
**Overview** Llama 2 was pretrained on 2 trillion tokens of data from publicly available sources. The fine-tuning data includes publicly available instruction datasets, as well as over one million new human-annotated examples. Neither the pretraining nor the fine-tuning datasets include Meta user data.

**Data Freshness** The pretraining data has a cutoff of September 2022, but some tuning data is more recent, up to July 2023.

## Evaluation Results

In this section, we report the results for the Llama 1 and Llama 2 models on standard academic benchmarks.For all the evaluations, we use our internal evaluations library.

|Model|Size|Code|Commonsense Reasoning|World Knowledge|Reading Comprehension|Math|MMLU|BBH|AGI Eval|
|---|---|---|---|---|---|---|---|---|---|
|Llama 1|7B|14.1|60.8|46.2|58.5|6.95|35.1|30.3|23.9|
|Llama 1|13B|18.9|66.1|52.6|62.3|10.9|46.9|37.0|33.9|
|Llama 1|33B|26.0|70.0|58.4|67.6|21.4|57.8|39.8|41.7|
|Llama 1|65B|30.7|70.7|60.5|68.6|30.8|63.4|43.5|47.6|
|Llama 2|7B|16.8|63.9|48.9|61.3|14.6|45.3|32.6|29.3|
|Llama 2|13B|24.5|66.9|55.4|65.8|28.7|54.8|39.4|39.1|
|Llama 2|70B|**37.5**|**71.9**|**63.6**|**69.4**|**35.2**|**68.9**|**51.2**|**54.2**|

**Overall performance on grouped academic benchmarks.** *Code:* We report the average pass@1 scores of our models on HumanEval and MBPP. *Commonsense Reasoning:* We report the average of PIQA, SIQA, HellaSwag, WinoGrande, ARC easy and challenge, OpenBookQA, and CommonsenseQA. We report 7-shot results for CommonSenseQA and 0-shot results for all other benchmarks. *World Knowledge:* We evaluate the 5-shot performance on NaturalQuestions and TriviaQA and report the average. *Reading Comprehension:* For reading comprehension, we report the 0-shot average on SQuAD, QuAC, and BoolQ. *MATH:* We report the average of the GSM8K (8 shot) and MATH (4 shot) benchmarks at top 1.

|||TruthfulQA|Toxigen|
|---|---|---|---|
|Llama 1|7B|27.42|23.00|
|Llama 1|13B|41.74|23.08|
|Llama 1|33B|44.19|22.57|
|Llama 1|65B|48.71|21.77|
|Llama 2|7B|33.29|**21.25**|
|Llama 2|13B|41.86|26.10|
|Llama 2|70B|**50.18**|24.60|

**Evaluation of pretrained LLMs on automatic safety benchmarks.** For TruthfulQA, we present the percentage of generations that are both truthful and informative (the higher the better). For ToxiGen, we present the percentage of toxic generations (the smaller the better).


|||TruthfulQA|Toxigen|
|---|---|---|---|
|Llama-2-Chat|7B|57.04|**0.00**|
|Llama-2-Chat|13B|62.18|**0.00**|
|Llama-2-Chat|70B|**64.14**|0.01|

**Evaluation of fine-tuned LLMs on different safety datasets.** Same metric definitions as above.

## Ethical Considerations and Limitations
Llama 2 is a new technology that carries risks with use. Testing conducted to date has been in English, and has not covered, nor could it cover all scenarios. For these reasons, as with all LLMs, Llama 2’s potential outputs cannot be predicted in advance, and the model may in some instances produce inaccurate, biased or other objectionable responses to user prompts. Therefore, before deploying any applications of Llama 2, developers should perform safety testing and tuning tailored to their specific applications of the model.

Please see the Responsible Use Guide available at [https://ai.meta.com/llama/responsible-use-guide/](https://ai.meta.com/llama/responsible-use-guide)

## Reporting Issues
Please report any software “bug,” or other problems with the models through one of the following means:
- Reporting issues with the model: [github.com/facebookresearch/llama](http://github.com/facebookresearch/llama)
- Reporting problematic content generated by the model: [developers.facebook.com/llama_output_feedback](http://developers.facebook.com/llama_output_feedback)
- Reporting bugs and security concerns: [facebook.com/whitehat/info](http://facebook.com/whitehat/info)

## Llama Model Index
|Model|Llama2|Llama2-hf|Llama2-chat|Llama2-chat-hf|
|---|---|---|---|---|
|7B| [Link](https://huggingface.co/meta-llama/Llama-2-7b) | [Link](https://huggingface.co/meta-llama/Llama-2-7b-hf) | [Link](https://huggingface.co/meta-llama/Llama-2-7b-chat) | [Link](https://huggingface.co/meta-llama/Llama-2-7b-chat-hf)|
|13B| [Link](https://huggingface.co/meta-llama/Llama-2-13b) | [Link](https://huggingface.co/meta-llama/Llama-2-13b-hf) | [Link](https://huggingface.co/meta-llama/Llama-2-13b-chat) | [Link](https://huggingface.co/meta-llama/Llama-2-13b-chat-hf)|
|70B| [Link](https://huggingface.co/meta-llama/Llama-2-70b) | [Link](https://huggingface.co/meta-llama/Llama-2-70b-hf) | [Link](https://huggingface.co/meta-llama/Llama-2-70b-chat) | [Link](https://huggingface.co/meta-llama/Llama-2-70b-chat-hf)|