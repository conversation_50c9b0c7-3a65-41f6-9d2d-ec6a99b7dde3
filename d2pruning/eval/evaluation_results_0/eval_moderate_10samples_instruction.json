{"selection_method": "moderate", "num_samples": 10, "format_type": "instruction", "model_name": "meta-llama/Llama-2-7b-hf", "training_results": {"training_time": 22.551298141479492, "num_epochs": 3, "learning_rate": 0.0002, "num_samples": 10, "final_loss": 1.6817061106363933, "total_parameters": 3517190144, "trainable_parameters": 16777216}, "output_dir": "finetuned_models/moderate_10samples_instruction", "evaluation_results": {"rouge_scores": {"rouge1": 0.115331773079663, "rouge2": 0.01686414176761905, "rougeL": 0.09755064531697913, "rougeLsum": 0.0979328196538472}, "generation_time": 1693.7780017852783, "num_test_samples": 100, "generated_summaries": ["The new policy was not well received by employees. Many of them feel that the new policy is overly strict and that it restricts their freedom to communicate with their clients in a way that suits them best.", "#Person1#: That's all.", "#Person1#: Sure thing, Mr. <PERSON>.", ">I feel bad about how much my car is adding to the pollution problem in this city.", "#Person1#: Great!"], "reference_summaries": ["Ms<PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "Ms<PERSON> <PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations."]}}