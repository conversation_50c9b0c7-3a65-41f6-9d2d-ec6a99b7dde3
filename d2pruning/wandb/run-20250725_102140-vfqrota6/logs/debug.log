2025-07-25 10:21:40,312 INFO    MainThread:3540473 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-25 10:21:40,313 INFO    MainThread:3540473 [wandb_setup.py:_flush():80] Configure stats pid to 3540473
2025-07-25 10:21:40,313 INFO    MainThread:3540473 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-25 10:21:40,313 INFO    MainThread:3540473 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-25 10:21:40,313 INFO    MainThread:3540473 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-25 10:21:40,313 INFO    MainThread:3540473 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250725_102140-vfqrota6/logs/debug.log
2025-07-25 10:21:40,313 INFO    MainThread:3540473 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250725_102140-vfqrota6/logs/debug-internal.log
2025-07-25 10:21:40,314 INFO    MainThread:3540473 [wandb_init.py:init():830] calling init triggers
2025-07-25 10:21:40,314 INFO    MainThread:3540473 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-07-25 10:21:40,314 INFO    MainThread:3540473 [wandb_init.py:init():871] starting backend
2025-07-25 10:21:41,772 INFO    MainThread:3540473 [wandb_init.py:init():874] sending inform_init request
2025-07-25 10:21:41,800 INFO    MainThread:3540473 [wandb_init.py:init():882] backend started and connected
2025-07-25 10:21:41,803 INFO    MainThread:3540473 [wandb_init.py:init():953] updated telemetry
2025-07-25 10:21:41,813 INFO    MainThread:3540473 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
