import os
import sys
import json
import pickle
import time
import logging
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any
from collections import Counter

import torch
import datasets
from datasets import load_dataset, Dataset
from sentence_transformers import SentenceTransformer
from transformers import <PERSON>Tokenizer, AutoModel, set_seed

sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
from data import CoresetSelection

logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


class DialogSumProcessor:
    def __init__(self, cache_dir: str = "./cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
    def load_dialogsum(self, split: str = "train") -> Dataset:
        cache_file = self.cache_dir / f"dialogsum_{split}.pkl"
        
        if cache_file.exists():
            logger.info(f"Loading cached DialogSum {split} dataset from {cache_file}")
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        
        logger.info(f"Loading DialogSum {split} dataset from HuggingFace")
        dataset = load_dataset("knkarthick/dialogsum", split=split)
        
        with open(cache_file, 'wb') as f:
            pickle.dump(dataset, f)
        logger.info(f"Cached DialogSum {split} dataset to {cache_file}")
        
        return dataset
    
    def preprocess_for_selection(self, dataset: Dataset) -> Dict[str, Any]:
        """
        Preprocess DialogSum data for selection algorithms.
        
        Returns:
            Dict containing:
            - dialogues: List of dialogue texts
            - summaries: List of summary texts  
            - combined_texts: List of dialogue + summary combined
            - metadata: Additional information
        """
        dialogues = []
        summaries = []
        combined_texts = []
        
        for example in dataset:
            dialogue = example['dialogue'].strip()
            summary = example['summary'].strip()
            
            combined = f"Dialogue: {dialogue}\nSummary: {summary}"
            
            dialogues.append(dialogue)
            summaries.append(summary)
            combined_texts.append(combined)
        
        return {
            'dialogues': dialogues,
            'summaries': summaries, 
            'combined_texts': combined_texts,
            'metadata': {
                'total_samples': len(dialogues),
                'avg_dialogue_length': np.mean([len(d.split()) for d in dialogues]),
                'avg_summary_length': np.mean([len(s.split()) for s in summaries])
            }
        }


class EmbeddingExtractor:
    
    def __init__(self, model_name: str = "meta-llama/Llama-2-7b-hf", cache_dir: str = "./cache"):
        self.model_name = model_name
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.model = None
        self.tokenizer = None
        self.use_llama = "llama" in model_name.lower()

    def _load_model(self):
        if self.model is None:
            if self.use_llama:
                logger.info(f"Loading LLaMA model for embeddings: {self.model_name}")
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.model = AutoModel.from_pretrained(
                    self.model_name,
                    torch_dtype=torch.float16,
                    device_map="auto",
                    load_in_4bit=True
                )
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
                self.model.eval()
            else:
                logger.info(f"Loading sentence transformer model: {self.model_name}")
                self.model = SentenceTransformer(self.model_name)
            
    def extract_embeddings(self, texts: List[str], cache_key: str) -> np.ndarray:
        cache_file = self.cache_dir / f"embeddings_{cache_key}_{self.model_name.replace('/', '_')}.npy"

        if cache_file.exists():
            logger.info(f"Loading cached embeddings from {cache_file}")
            return np.load(cache_file)

        self._load_model()
        logger.info(f"Extracting embeddings for {len(texts)} texts using {self.model_name}")

        batch_size = 8 if self.use_llama else 32  
        embeddings = []

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]

            if self.use_llama:
                batch_embeddings = self._extract_llama_embeddings(batch_texts)
            else:
                batch_embeddings = self.model.encode(
                    batch_texts,
                    batch_size=batch_size,
                    show_progress_bar=True,
                    convert_to_numpy=True,
                    normalize_embeddings=True
                )

            embeddings.append(batch_embeddings)

        embeddings = np.vstack(embeddings)

        np.save(cache_file, embeddings)
        logger.info(f"Cached embeddings to {cache_file}")

        return embeddings

    def _extract_llama_embeddings(self, texts: List[str]) -> np.ndarray:
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=512,
            return_tensors="pt"
        )

        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}

        with torch.no_grad():
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state

            attention_mask = inputs['attention_mask']
            mask_expanded = attention_mask.unsqueeze(-1).expand(embeddings.size()).float()
            sum_embeddings = torch.sum(embeddings * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            embeddings = sum_embeddings / sum_mask

            embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)

        return embeddings.cpu().numpy()


def create_embedding_based_scores(embeddings: np.ndarray, dialogues: List[str], summaries: List[str]) -> Dict[str, torch.Tensor]:
    num_samples = len(dialogues)

    targets = torch.zeros(num_samples, dtype=torch.long)


    centroid = np.mean(embeddings, axis=0)
    distances = np.linalg.norm(embeddings - centroid, axis=1)
    diversity_scores = torch.from_numpy(distances).float()

    dialogue_lengths = torch.tensor([len(d.split()) for d in dialogues], dtype=torch.float32)
    summary_lengths = torch.tensor([len(s.split()) for s in summaries], dtype=torch.float32)

    diversity_scores = (diversity_scores - diversity_scores.min()) / (diversity_scores.max() - diversity_scores.min() + 1e-8)
    dialogue_lengths = (dialogue_lengths - dialogue_lengths.min()) / (dialogue_lengths.max() - dialogue_lengths.min() + 1e-8)
    summary_lengths = (summary_lengths - summary_lengths.min()) / (summary_lengths.max() - summary_lengths.min() + 1e-8)

    return {
        'targets': targets,
        'confidence': 1.0 - diversity_scores,  
        'entropy': diversity_scores,           
        'dialogue_length': dialogue_lengths,
        'summary_length': summary_lengths,
    }


def k_center_greedy_selection(embeddings: np.ndarray, num_samples: int) -> np.ndarray:
    n_samples = embeddings.shape[0]
    selected_indices = []

    # Start with the sample closest to the centroid
    centroid = np.mean(embeddings, axis=0)
    distances_to_centroid = np.linalg.norm(embeddings - centroid, axis=1)
    first_idx = np.argmin(distances_to_centroid)
    selected_indices.append(first_idx)

    for _ in range(num_samples - 1):
        max_min_distance = -1
        next_idx = -1

        for i in range(n_samples):
            if i in selected_indices:
                continue

            min_distance = float('inf')
            for selected_idx in selected_indices:
                distance = np.linalg.norm(embeddings[i] - embeddings[selected_idx])
                min_distance = min(min_distance, distance)

            if min_distance > max_min_distance:
                max_min_distance = min_distance
                next_idx = i

        if next_idx != -1:
            selected_indices.append(next_idx)

    return np.array(selected_indices)


def main():
    parser = argparse.ArgumentParser(description="DialogSum Statistical Data Selection")
    parser.add_argument("--num_samples", type=int, default=10, 
                       help="Number of samples to select")
    parser.add_argument("--selection_method", type=str, default="moderate",
                       choices=["random", "moderate", "k_center", "diversity"],
                       help="Selection method to use")
    parser.add_argument("--embedding_model", type=str, default="meta-llama/Llama-2-7b-hf",
                       help="Model for embeddings (LLaMA or sentence transformer)")
    parser.add_argument("--cache_dir", type=str, default="./cache",
                       help="Directory for caching")
    parser.add_argument("--output_dir", type=str, default="./results",
                       help="Output directory for results")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    parser.add_argument("--enable_wandb", action="store_true", default=False,
                       help="Enable Weights & Biases logging")
    parser.add_argument("--wandb_run_name", type=str, default=None,
                       help="W&B run name (optional)")

    args = parser.parse_args()
    
    set_seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    wandb_logger = None
    if args.enable_wandb:
        from wandb_logger import setup_wandb_logging

        config = {
            "selection_method": args.selection_method,
            "num_samples": args.num_samples,
            "embedding_model": args.embedding_model,
            "seed": args.seed,
            "task": "data_selection"
        }

        run_name = args.wandb_run_name or f"selection_{args.selection_method}_{args.num_samples}"
        wandb_logger = setup_wandb_logging(config, run_name)

    logger.info("Starting DialogSum data selection pipeline")
    logger.info(f"Selection method: {args.selection_method}")
    logger.info(f"Number of samples: {args.num_samples}")
    logger.info(f"Embedding model: {args.embedding_model}")
    
    processor = DialogSumProcessor(cache_dir=args.cache_dir)
    extractor = EmbeddingExtractor(model_name=args.embedding_model, cache_dir=args.cache_dir)
    
    logger.info("Loading DialogSum dataset...")
    train_dataset = processor.load_dialogsum("train")
    test_dataset = processor.load_dialogsum("test")
    
    logger.info(f"Train dataset size: {len(train_dataset)}")
    logger.info(f"Test dataset size: {len(test_dataset)}")
    
    train_data = processor.preprocess_for_selection(train_dataset)
    logger.info(f"Preprocessed {train_data['metadata']['total_samples']} training samples")
    logger.info(f"Average dialogue length: {train_data['metadata']['avg_dialogue_length']:.1f} words")
    logger.info(f"Average summary length: {train_data['metadata']['avg_summary_length']:.1f} words")

    if wandb_logger:
        wandb_logger.log_dataset_info(
            train_size=len(train_dataset),
            test_size=len(test_dataset),
            avg_dialogue_length=train_data['metadata']['avg_dialogue_length'],
            avg_summary_length=train_data['metadata']['avg_summary_length']
        )

    logger.info("Extracting embeddings...")
    embedding_start_time = time.time()
    embeddings = extractor.extract_embeddings(
        train_data['combined_texts'],
        cache_key="dialogsum_train_combined"
    )
    embedding_time = time.time() - embedding_start_time
    logger.info(f"Extracted embeddings shape: {embeddings.shape}")

    if wandb_logger:
        wandb_logger.log_embedding_info(
            embedding_shape=embeddings.shape,
            embedding_model=args.embedding_model,
            extraction_time=embedding_time
        )
    
    logger.info(f"Performing {args.selection_method} selection...")
    start_time = time.time()

    if args.selection_method == "random":
        selected_indices = CoresetSelection.random_selection(
            total_num=len(train_data['dialogues']),
            num=args.num_samples
        ).numpy()
    elif args.selection_method == "moderate":
        embedding_scores = create_embedding_based_scores(
            embeddings, train_data['dialogues'], train_data['summaries']
        )
        ratio = args.num_samples / len(train_data['dialogues'])

        selected_indices = CoresetSelection.moderate_selection(
            data_score=embedding_scores,
            ratio=ratio,
            features=embeddings
        )
    elif args.selection_method == "k_center":
        selected_indices = k_center_greedy_selection(embeddings, args.num_samples)
    elif args.selection_method == "diversity":
        embedding_scores = create_embedding_based_scores(
            embeddings, train_data['dialogues'], train_data['summaries']
        )
        diversity_scores = embedding_scores['entropy']
        selected_indices = torch.argsort(diversity_scores, descending=True)[:args.num_samples].numpy()

    selection_time = time.time() - start_time
    logger.info(f"Selection completed in {selection_time:.2f} seconds")
    logger.info(f"Selected indices: {selected_indices}")

    diversity_scores = None
    if args.selection_method in ["moderate", "diversity"]:
        embedding_scores = create_embedding_based_scores(
            embeddings, train_data['dialogues'], train_data['summaries']
        )
        diversity_scores = embedding_scores['entropy'].numpy()

    if wandb_logger:
        wandb_logger.log_selection_results(
            selection_method=args.selection_method,
            selected_indices=selected_indices.tolist(),
            selection_time=selection_time,
            total_samples=len(train_data['dialogues']),
            diversity_scores=diversity_scores
        )

    results = {
        'selection_method': args.selection_method,
        'num_samples': args.num_samples,
        'selected_indices': selected_indices.tolist(),
        'selection_time': selection_time,
        'embedding_model': args.embedding_model,
        'train_dataset_size': len(train_data['dialogues']),
        'selected_dialogues': [train_data['dialogues'][i] for i in selected_indices],
        'selected_summaries': [train_data['summaries'][i] for i in selected_indices],
        'metadata': train_data['metadata']
    }

    results_file = output_dir / f"selection_results_{args.selection_method}_{args.num_samples}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    if wandb_logger:
        wandb_logger.finish_experiment()

    logger.info(f"Results saved to {results_file}")
    logger.info("DialogSum data selection completed successfully!")


if __name__ == "__main__":
    main()
