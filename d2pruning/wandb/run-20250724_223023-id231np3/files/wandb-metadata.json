{"os": "Linux-6.8.0-60-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.12", "startedAt": "2025-07-24T15:30:23.697909Z", "args": ["--selection_methods", "random", "moderate", "k_center", "diversity", "--num_samples", "10", "--num_epochs", "3", "--format_type", "chat", "--force_regenerate"], "program": "/storage/nammt/data_selection_for_assistant_model/d2pruning/run_dialogsum_experiment.py", "codePath": "run_dialogsum_experiment.py", "codePathLocal": "run_dialogsum_experiment.py", "git": {"remote": "https://github.com/adymaharana/d2pruning.git", "commit": "bf1ea46b22cc79549df421fcd92c9cd2d3408008"}, "email": "<EMAIL>", "root": "/storage/nammt/data_selection_for_assistant_model/d2pruning", "host": "csews-Precision-7920-Tower", "executable": "/storage/nammt/data_selection_for_assistant_model/venv/bin/python", "cpu_count": 6, "cpu_count_logical": 6, "gpu": "NVIDIA RTX A4000", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "33291022336"}, "gpu_nvidia": [{"name": "NVIDIA RTX A4000", "memoryTotal": "17171480576", "cudaCores": 6144, "architecture": "Ampere", "uuid": "GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a"}], "cudaVersion": "12.9", "writerId": "3h2zr5uezvfpomivdxwx3fulhcly4c4z"}