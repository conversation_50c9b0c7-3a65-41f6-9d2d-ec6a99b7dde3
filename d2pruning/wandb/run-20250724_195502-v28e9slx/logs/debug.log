2025-07-24 19:55:02,293 INFO    MainThread:2570560 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_setup.py:_flush():80] Configure stats pid to 2570560
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/settings
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_195502-v28e9slx/logs/debug.log
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_195502-v28e9slx/logs/debug-internal.log
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_init.py:init():830] calling init triggers
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_methods': ['random', 'moderate', 'k_center', 'diversity'], 'num_samples': 10, 'model_name': 'meta-llama/Llama-2-7b-hf', 'format_type': 'chat', 'num_epochs': 3, 'max_test_samples': 500, 'experiment_type': 'dialogsum_data_selection', '_wandb': {}}
2025-07-24 19:55:02,294 INFO    MainThread:2570560 [wandb_init.py:init():871] starting backend
2025-07-24 19:55:02,504 WARNING MainThread:2570560 [wandb_init.py:init():1610] [no run ID] interrupted
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 1606, in init
    return wi.init(run_settings, run_config, run_printer)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 873, in init
    service = self._wl.ensure_service()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/wandb_setup.py", line 325, in ensure_service
    self._connection = service_connection.connect_to_service(self._settings)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/service/service_connection.py", line 33, in connect_to_service
    return _start_and_connect_service(settings)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/service/service_connection.py", line 45, in _start_and_connect_service
    proc = service_process.start(settings)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/service/service_process.py", line 33, in start
    return _launch_server(settings)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/service/service_process.py", line 105, in _launch_server
    token = service_port_file.poll_for_token(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/wandb/sdk/lib/service/service_port_file.py", line 61, in poll_for_token
    _SLEEP(max(0, min(0.2, end_time - _MONOTONIC())))
KeyboardInterrupt
