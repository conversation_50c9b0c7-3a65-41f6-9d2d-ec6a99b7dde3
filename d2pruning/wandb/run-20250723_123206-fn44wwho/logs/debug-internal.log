{"time":"2025-07-23T12:32:06.204295306+07:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-23T12:32:06.572931488+07:00","level":"INFO","msg":"stream: created new stream","id":"fn44wwho"}
{"time":"2025-07-23T12:32:06.5730029+07:00","level":"INFO","msg":"stream: started","id":"fn44wwho"}
{"time":"2025-07-23T12:32:06.573060009+07:00","level":"INFO","msg":"handler: started","stream_id":"fn44wwho"}
{"time":"2025-07-23T12:32:06.573077992+07:00","level":"INFO","msg":"sender: started","stream_id":"fn44wwho"}
{"time":"2025-07-23T12:32:06.573154602+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"fn44wwho"}
{"time":"2025-07-23T12:32:44.717316618+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.677360946,"progress":"31.1KB/31.1KB"},{"desc":"uploading wandb-summary.json","runtime_seconds":0.677342364,"progress":"739B/739B"},{"desc":"uploading config.yaml","runtime_seconds":0.400487596,"progress":"2.9KB/2.9KB"}],"total_operations":3}}
{"time":"2025-07-23T12:32:45.203694059+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-23T12:32:45.669938794+07:00","level":"INFO","msg":"stream: closing","id":"fn44wwho"}
{"time":"2025-07-23T12:32:45.669983209+07:00","level":"INFO","msg":"handler: closed","stream_id":"fn44wwho"}
{"time":"2025-07-23T12:32:45.670006809+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"fn44wwho"}
{"time":"2025-07-23T12:32:45.670047923+07:00","level":"INFO","msg":"sender: closed","stream_id":"fn44wwho"}
{"time":"2025-07-23T12:32:45.670174002+07:00","level":"INFO","msg":"stream: closed","id":"fn44wwho"}
