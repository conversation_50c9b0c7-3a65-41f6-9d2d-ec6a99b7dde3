07/25/2025 06:48:17 - INFO - __main__ - Loading model from d2pruning/finetuned_models/moderate_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
07/25/2025 06:48:18 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:23<00:00, 11.94s/it]
07/25/2025 06:48:45 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 06:48:45 - INFO - __main__ - Starting summary generation...
Generating Summaries:   0%|          | 0/13 [00:00<?, ?it/s]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
Generating Summaries:   8%|▊         | 1/13 [00:38<07:45, 38.77s/it]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
Generating Summaries:   8%|▊         | 1/13 [01:18<15:41, 78.42s/it]
07/25/2025 06:50:03 - ERROR - __main__ - Failed to evaluate model moderate_10samples_chat: list index out of range
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 291, in main
    predictions, references = evaluator.generate_summaries(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 157, in generate_summaries
    cleaned_preds = [pred.split("Summary:")[1].strip() for pred in decoded_preds]
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 157, in <listcomp>
    cleaned_preds = [pred.split("Summary:")[1].strip() for pred in decoded_preds]
IndexError: list index out of range
