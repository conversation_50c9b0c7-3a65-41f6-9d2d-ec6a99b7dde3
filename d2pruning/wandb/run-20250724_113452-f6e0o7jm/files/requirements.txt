beautifulsoup4==4.13.4
tornado==6.5.1
pyarrow==20.0.0
safetensors==0.5.3
nvidia-cufile-cu12==1.11.1.6
anyio==4.9.0
notebook==7.4.4
jupyter-events==0.12.0
bleach==6.2.0
sympy==1.14.0
MarkupSafe==3.0.2
nvidia-cuda-runtime-cu12==12.6.77
scikit-learn==1.7.0
annotated-types==0.7.0
parso==0.8.4
triton==3.3.1
sniffio==1.3.1
nvidia-cusparse-cu12==********
pyzmq==27.0.0
nbclient==0.10.2
wandb==0.21.0
charset-normalizer==3.4.2
async-lru==2.0.5
rfc3986-validator==0.1.1
Jinja2==3.1.6
attrs==25.3.0
protobuf==6.31.1
aiohappyeyeballs==2.6.1
webencodings==0.5.1
pillow==11.3.0
jupyterlab_widgets==3.0.15
jsonpointer==3.0.0
defusedxml==0.7.1
argon2-cffi==25.1.0
lark==1.2.2
comm==0.2.2
nvidia-cuda-nvrtc-cu12==12.6.77
pip==22.0.2
tinycss2==1.4.0
platformdirs==4.3.8
hf-xet==1.1.5
rfc3339-validator==0.1.4
certifi==2025.7.14
asttokens==3.0.0
six==1.17.0
pycparser==2.22
jupyter==1.1.1
joblib==1.5.1
PyYAML==6.0.2
traitlets==5.14.3
aiohttp==3.12.14
jupyterlab==4.4.5
torchvision==0.22.1
nbconvert==7.16.6
nvidia-nvtx-cu12==12.6.77
evaluate==0.4.5
arrow==1.3.0
mistune==3.1.3
filelock==3.18.0
jsonschema==4.25.0
fqdn==1.5.1
terminado==0.18.1
absl-py==2.3.1
typing_extensions==4.14.1
yarl==1.20.1
webcolors==24.11.1
executing==2.2.0
pydantic_core==2.33.2
GitPython==3.1.44
mpmath==1.3.0
ipywidgets==8.1.7
ipykernel==6.30.0
pydantic==2.11.7
rpds-py==0.26.0
pytz==2025.2
tzdata==2025.2
gitdb==4.0.12
widgetsnbextension==4.0.14
typing-inspection==0.4.1
nvidia-cudnn-cu12==********
types-python-dateutil==2.9.0.20250708
jsonschema-specifications==2025.4.1
urllib3==2.5.0
tokenizers==0.21.2
scipy==1.15.3
python-dateutil==2.9.0.post0
notebook_shim==0.2.4
debugpy==1.8.15
soupsieve==2.7
transformers==4.53.2
prompt_toolkit==3.0.51
prometheus_client==0.22.1
ipython==8.37.0
jedi==0.19.2
bitsandbytes==0.46.1
decorator==5.2.1
wcwidth==0.2.13
nbformat==5.10.4
ptyprocess==0.7.0
tomli==2.2.1
huggingface-hub==0.33.4
regex==2024.11.6
rouge-score==0.1.2
referencing==0.36.2
matplotlib-inline==0.1.7
jupyterlab_pygments==0.3.0
sentence-transformers==5.0.0
stack-data==0.6.3
jupyter-console==6.6.3
idna==3.10
httpx==0.28.1
Pygments==2.19.2
async-timeout==5.0.1
sentry-sdk==2.33.0
propcache==0.3.2
httpcore==1.0.9
smmap==5.0.2
Send2Trash==1.8.3
xxhash==3.5.0
nvidia-cublas-cu12==********
overrides==7.7.0
nvidia-cusparselt-cu12==0.6.3
websocket-client==1.8.0
nvidia-nvjitlink-cu12==12.6.85
jupyter_core==5.8.1
fsspec==2025.3.0
nvidia-cufft-cu12==********
tqdm==4.67.1
fastjsonschema==2.21.1
argon2-cffi-bindings==21.2.0
frozenlist==1.7.0
exceptiongroup==1.3.0
peft==0.16.0
nvidia-curand-cu12==*********
pandocfilters==1.5.1
pure_eval==0.2.3
dill==0.3.8
accelerate==1.9.0
nest-asyncio==1.6.0
nvidia-nccl-cu12==2.26.2
jupyter-lsp==2.2.6
multidict==6.6.3
pandas==2.3.1
pexpect==4.9.0
multiprocess==0.70.16
torch==2.7.1
jupyter_server==2.16.0
packaging==25.0
jupyterlab_server==2.27.3
jupyter_server_terminals==0.5.3
threadpoolctl==3.6.0
jupyter_client==8.6.3
nltk==3.9.1
isoduration==20.11.0
networkx==3.4.2
numpy==2.2.6
json5==0.12.0
h11==0.16.0
datasets==4.0.0
uri-template==1.3.0
nvidia-cusolver-cu12==********
requests==2.32.4
rfc3987-syntax==1.1.0
python-json-logger==3.3.0
nvidia-cuda-cupti-cu12==12.6.80
aiosignal==1.4.0
click==8.2.1
setuptools==59.6.0
cffi==1.17.1
babel==2.17.0
psutil==7.0.0
