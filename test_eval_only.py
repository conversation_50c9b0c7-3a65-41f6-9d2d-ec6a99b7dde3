#!/usr/bin/env python3
"""
Quick Test for the Evaluation Script Only

This script tests only the evaluation component by:
1. Loading an existing fine-tuned model (or using a pre-trained model)
2. Evaluating it on a very small test set
3. Displaying results
"""

import os
import sys
import argparse
import logging
import json
from pathlib import Path

# Set up logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description="Test only the evaluation component")
    parser.add_argument("--model_path", type=str, default=None,
                        help="Path to fine-tuned model to evaluate (optional, uses base model if not provided)")
    parser.add_argument("--base_model", type=str, default="meta-llama/Llama-2-7b-hf",
                        help="Base model name")
    parser.add_argument("--max_test_samples", type=int, default=5,
                        help="Maximum number of test samples to evaluate (keep small for quick test)")
    parser.add_argument("--enable_wandb", action="store_true",
                        help="Enable Weights & Biases logging")
    parser.add_argument("--output_dir", type=str, default="./eval_test_results",
                        help="Directory to save evaluation results")
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # If no model path provided, use base model directly
    model_paths = []
    if args.model_path:
        model_paths.append(args.model_path)
        logger.info(f"Will evaluate fine-tuned model: {args.model_path}")
    else:
        # Just use base model for testing
        model_paths.append(args.base_model)
        logger.info(f"No fine-tuned model provided, will evaluate base model: {args.base_model}")
    
    # Prepare command
    cmd = [
        sys.executable, "d2pruning/evaluate_summaries.py",
        "--model_dirs"
    ] + model_paths + [
        "--base_model", args.base_model,
        "--max_test_samples", str(args.max_test_samples),
        "--output_dir", str(output_dir),
        "--cache_dir", "d2pruning/cache",
        "--batch_size", "1",  # Small batch size for quick test
        "--seed", "42"        # Fixed seed for reproducibility
    ]
    
    if args.enable_wandb:
        cmd.append("--enable_wandb")
    
    # Print the command that would be run
    cmd_str = " ".join(cmd)
    logger.info(f"Running command: {cmd_str}")
    
    # Execute command
    import subprocess
    process = subprocess.run(cmd, capture_output=True, text=True)
    
    # Check for errors
    if process.returncode != 0:
        logger.error(f"Evaluation failed with code {process.returncode}")
        logger.error(f"STDOUT: {process.stdout}")
        logger.error(f"STDERR: {process.stderr}")
        return
    
    # Print stdout
    logger.info("===== Evaluation Output =====")
    for line in process.stdout.split('\n'):
        logger.info(line)
    
    # Check and display results
    comparison_file = output_dir / "comparison_summary.json"
    if comparison_file.exists():
        logger.info(f"Results from {comparison_file}:")
        try:
            with open(comparison_file, 'r') as f:
                results = json.load(f)
                for model, scores in results.items():
                    logger.info(f"Model: {model}")
                    logger.info(f"  ROUGE-1: {scores.get('rouge1', 0):.4f}")
                    logger.info(f"  ROUGE-2: {scores.get('rouge2', 0):.4f}")
                    logger.info(f"  ROUGE-L: {scores.get('rougeL', 0):.4f}")
        except json.JSONDecodeError:
            logger.error(f"Could not parse {comparison_file} as JSON")
            with open(comparison_file, 'r') as f:
                logger.info(f"File contents: {f.read()}")
    else:
        logger.warning(f"No results file found at {comparison_file}")
        # List all files in output directory
        logger.info(f"Files in {output_dir}:")
        for file in output_dir.glob("*"):
            logger.info(f"  {file.name}")

if __name__ == "__main__":
    main()
