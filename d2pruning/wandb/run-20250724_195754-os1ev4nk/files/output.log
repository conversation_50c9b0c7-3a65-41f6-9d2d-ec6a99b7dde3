07/24/2025 19:57:55 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 19:57:55 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/os1ev4nk
07/24/2025 19:57:55 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 19:57:57 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|██████████| 2/2 [00:51<00:00, 25.51s/it]
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484
07/24/2025 19:58:50 - INFO - __main__ - Preparing training dataset...
Map: 100%|██████████| 10/10 [00:00<00:00, 82.77 examples/s]
07/24/2025 19:59:13 - INFO - __main__ - Starting fine-tuning...
07/24/2025 19:59:13 - INFO - wandb_logger - Logged training start for moderate to W&B
Using auto half precision backend
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
***** Running training *****
  Num examples = 10
  Num Epochs = 3
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 4
  Gradient Accumulation steps = 4
  Total optimization steps = 9
  Number of trainable parameters = 16,777,216
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"
 33%|███▎      | 3/9 [00:08<00:13,  2.25s/it]Saving model checkpoint to finetuned_models/moderate_10samples_chat/checkpoint-3
{'loss': 2.051, 'grad_norm': 0.5073608756065369, 'learning_rate': 0.0, 'epoch': 0.4}
{'loss': 2.052, 'grad_norm': 0.47231969237327576, 'learning_rate': 2.0000000000000003e-06, 'epoch': 0.8}
{'loss': 1.9974, 'grad_norm': 0.4352172017097473, 'learning_rate': 4.000000000000001e-06, 'epoch': 1.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in finetuned_models/moderate_10samples_chat/checkpoint-3/tokenizer_config.json
Special tokens file saved in finetuned_models/moderate_10samples_chat/checkpoint-3/special_tokens_map.json
 67%|██████▋   | 6/9 [00:14<00:05,  1.85s/it]Saving model checkpoint to finetuned_models/moderate_10samples_chat/checkpoint-6
{'loss': 2.1044, 'grad_norm': 0.46897220611572266, 'learning_rate': 6e-06, 'epoch': 1.4}
{'loss': 2.0206, 'grad_norm': 0.4345324635505676, 'learning_rate': 8.000000000000001e-06, 'epoch': 1.8}
{'loss': 1.9174, 'grad_norm': 0.560967206954956, 'learning_rate': 1e-05, 'epoch': 2.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in finetuned_models/moderate_10samples_chat/checkpoint-6/tokenizer_config.json
Special tokens file saved in finetuned_models/moderate_10samples_chat/checkpoint-6/special_tokens_map.json
100%|██████████| 9/9 [00:19<00:00,  1.75s/it]Saving model checkpoint to finetuned_models/moderate_10samples_chat/checkpoint-9
{'loss': 1.9927, 'grad_norm': 0.5169804692268372, 'learning_rate': 1.2e-05, 'epoch': 2.4}
{'loss': 2.0923, 'grad_norm': 0.4527921974658966, 'learning_rate': 1.4000000000000001e-05, 'epoch': 2.8}
{'loss': 1.9432, 'grad_norm': 0.498984158039093, 'learning_rate': 1.6000000000000003e-05, 'epoch': 3.0}
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in finetuned_models/moderate_10samples_chat/checkpoint-9/tokenizer_config.json
Special tokens file saved in finetuned_models/moderate_10samples_chat/checkpoint-9/special_tokens_map.json


Training completed. Do not forget to share your model on huggingface.co/models =)


100%|██████████| 9/9 [00:21<00:00,  2.36s/it]
{'train_runtime': 21.2712, 'train_samples_per_second': 1.41, 'train_steps_per_second': 0.423, 'train_loss': 2.019007775518629, 'epoch': 3.0}
07/24/2025 19:59:36 - INFO - wandb_logger - Logged training completion for moderate to W&B
Saving model checkpoint to finetuned_models/moderate_10samples_chat
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in finetuned_models/moderate_10samples_chat/tokenizer_config.json
Special tokens file saved in finetuned_models/moderate_10samples_chat/special_tokens_map.json
07/24/2025 19:59:37 - INFO - __main__ - Fine-tuning completed in 21.63 seconds
