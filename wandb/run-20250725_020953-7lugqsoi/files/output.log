07/25/2025 02:09:54 - INFO - __main__ - Loading model from d2pruning/finetuned_models/diversity_10samples_chat on device: cuda
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.
model-00001-of-00002.safetensors: 100%|███████████████████████████████████████| 9.98G/9.98G [06:40<00:00, 24.9MB/s]
Fetching 2 files: 100%|█████████████████████████████████████████████████████████████| 2/2 [06:41<00:00, 200.68s/it]
07/25/2025 02:16:38 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████| 2/2 [00:28<00:00, 14.19s/it]
generation_config.json: 100%|██████████████████████████████████████████████████████| 188/188 [00:00<00:00, 331kB/s]
07/25/2025 02:17:09 - INFO - __main__ - Model and tokenizer loaded successfully.
07/25/2025 02:17:09 - INFO - __main__ - Starting summary generation...
Generating Summaries:   0%|                                                                  | 0/2 [00:00<?, ?it/s]A decoder-only architecture is being used, but right-padding was detected! For correct generation results, please set `padding_side='left'` when initializing the tokenizer.
/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/bitsandbytes/nn/modules.py:457: UserWarning: Input type into Linear4bit is torch.float16, but bnb_4bit_compute_dtype=torch.float32 (default). This will lead to slow inference or training speed.
  warnings.warn(
Generating Summaries: 100%|██████████████████████████████████████████████████████████| 2/2 [01:01<00:00, 30.65s/it]
07/25/2025 02:18:11 - INFO - __main__ - Generated 5 summaries.
07/25/2025 02:18:11 - INFO - __main__ - Computing and logging metrics...
Calculating ROUGE:   0%|                                                                     | 0/1 [00:00<?, ?it/s]07/25/2025 02:18:11 - INFO - absl - Using default tokenizer.
07/25/2025 02:18:11 - INFO - absl - Using default tokenizer.
Calculating ROUGE:   0%|                                                                     | 0/1 [00:00<?, ?it/s]
07/25/2025 02:18:11 - ERROR - __main__ - Failed to evaluate model diversity_10samples_chat: 'numpy.float64' object has no attribute 'mid'
Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 276, in main
    rouge_scores = evaluator.compute_and_log_metrics(predictions, references)
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/evaluate_summaries.py", line 184, in compute_and_log_metrics
    "eval/rouge1": agg_scores["rouge1"].mid.fmeasure,
AttributeError: 'numpy.float64' object has no attribute 'mid'. Did you mean: 'min'?
