07/25/2025 10:19:52 - INFO - __main__ - Logging to ../logs/finetune_20250725_101952.log
07/25/2025 10:19:52 - INFO - __main__ - Loading selection results from ./results/selection_results_d2pruning_10.json
07/25/2025 10:19:52 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 10:19:55 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).
07/25/2025 10:21:16 - INFO - __main__ - Preparing training dataset...
07/25/2025 10:21:39 - INFO - __main__ - Starting fine-tuning...
07/25/2025 10:22:07 - INFO - __main__ - Fine-tuning completed in 27.58 seconds
07/25/2025 10:22:07 - INFO - __main__ - Training results saved to finetuned_models/d2pruning_10samples_chat/training_results.json
07/25/2025 10:22:07 - INFO - __main__ - Fine-tuning completed successfully!
07/25/2025 10:22:07 - INFO - __main__ - Fine-tuning complete.
