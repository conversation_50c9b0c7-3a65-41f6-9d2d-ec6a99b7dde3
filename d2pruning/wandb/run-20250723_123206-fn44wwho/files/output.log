07/23/2025 12:32:07 - INFO - wandb_logger - Initialized W&B run: selection_random_10
07/23/2025 12:32:07 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/fn44wwho
07/23/2025 12:32:07 - INFO - __main__ - Starting DialogSum data selection pipeline
07/23/2025 12:32:07 - INFO - __main__ - Selection method: random
07/23/2025 12:32:07 - INFO - __main__ - Number of samples: 10
07/23/2025 12:32:07 - INFO - __main__ - Embedding model: all-MiniLM-L6-v2
07/23/2025 12:32:07 - INFO - __main__ - Loading DialogSum dataset...
07/23/2025 12:32:07 - INFO - __main__ - Loading DialogSum train dataset from HuggingFace
07/23/2025 12:32:11 - INFO - __main__ - Cached DialogSum train dataset to cache/dialogsum_train.pkl
07/23/2025 12:32:11 - INFO - __main__ - Loading DialogSum test dataset from HuggingFace
07/23/2025 12:32:13 - INFO - __main__ - Cached DialogSum test dataset to cache/dialogsum_test.pkl
07/23/2025 12:32:13 - INFO - __main__ - Train dataset size: 12460
07/23/2025 12:32:13 - INFO - __main__ - Test dataset size: 1500
07/23/2025 12:32:14 - INFO - __main__ - Preprocessed 12460 training samples
07/23/2025 12:32:14 - INFO - __main__ - Average dialogue length: 131.0 words
07/23/2025 12:32:14 - INFO - __main__ - Average summary length: 22.9 words
07/23/2025 12:32:14 - INFO - wandb_logger - Logged dataset information to W&B
07/23/2025 12:32:14 - INFO - __main__ - Extracting embeddings...
07/23/2025 12:32:14 - INFO - __main__ - Loading sentence transformer model: all-MiniLM-L6-v2
07/23/2025 12:32:14 - INFO - sentence_transformers.SentenceTransformer - Use pytorch device_name: cuda:0
07/23/2025 12:32:14 - INFO - sentence_transformers.SentenceTransformer - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
07/23/2025 12:32:19 - INFO - __main__ - Extracting embeddings for 12460 texts using all-MiniLM-L6-v2
Batches: 100%|██████████| 1/1 [00:00<00:00,  1.37it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.28it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.72it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.28it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.06it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.07it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.35it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.96it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.42it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.20it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.98it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.75it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.30it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.67it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.92it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.32it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.17it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.79it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.81it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.61it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.72it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.44it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.83it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.42it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.48it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.20it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.34it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 13.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.42it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.20it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.65it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.59it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.89it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.98it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.12it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.65it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.59it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.28it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.26it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.98it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.91it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.79it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.02it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.62it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.37it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.56it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.45it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.28it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.22it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.39it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.48it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.77it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.57it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.86it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.28it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.85it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.00it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.11it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.89it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.42it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.83it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.74it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.70it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.04it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.47it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.52it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.45it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.10it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.80it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.07it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.06it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.36it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.45it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.86it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.92it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.21it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.63it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.99it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.90it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.08it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.91it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.77it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.82it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.61it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.39it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.50it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.55it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.32it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.46it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.08it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.86it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.13it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.99it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.98it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.75it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.02it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.87it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.79it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.56it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.49it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.78it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.32it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.90it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.74it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 21.56it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.06it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.97it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.73it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.66it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.37it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.99it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.52it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.75it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.66it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.86it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.92it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.63it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.60it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.31it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.75it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.26it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.55it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.55it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.33it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.40it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.90it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.34it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.88it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.19it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.41it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.93it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.89it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.92it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.67it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.71it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.76it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.12it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.04it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.56it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.67it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.27it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.54it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.68it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.48it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.44it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.46it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.27it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.85it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.40it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.21it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.29it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.10it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.72it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.39it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.94it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.77it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.19it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.95it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.16it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.11it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.63it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.65it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.50it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.81it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.81it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.53it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.54it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.66it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.66it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.85it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.83it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.96it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.39it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.77it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.74it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.85it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.57it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.83it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.20it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.05it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.08it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.31it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.35it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.27it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.70it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.52it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.58it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.69it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.52it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.41it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.69it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.70it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.59it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.09it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 14.69it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 14.24it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.37it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.45it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.88it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.76it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.59it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.28it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.88it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.59it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.78it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.71it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.73it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.20it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.44it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.05it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.67it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.04it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.85it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.68it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.82it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.83it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.27it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.23it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.36it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.84it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.74it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.33it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.10it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.92it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.17it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.98it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.41it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.54it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.45it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.88it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.36it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.03it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.15it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.24it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.55it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.94it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.18it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.19it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.56it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.39it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.26it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.53it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.56it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.20it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 14.95it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.58it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.96it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.71it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.71it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.56it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.63it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.06it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.77it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.57it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.86it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.66it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.30it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.13it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.58it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.63it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.43it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 14.87it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 14.90it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.76it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.84it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.13it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.81it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.54it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.08it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.34it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.81it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.75it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.35it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.84it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.54it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.22it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.04it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.61it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.11it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.20it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.02it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.30it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.23it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.45it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.63it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.76it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.55it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.88it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.86it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.90it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.33it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.41it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.11it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.71it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.16it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.31it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.68it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.65it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.90it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.82it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.46it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.16it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.03it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.22it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.62it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.04it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.49it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.04it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.11it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.18it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.19it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.47it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.86it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.26it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.37it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.14it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.73it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.61it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.17it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.83it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.43it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 15.94it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.76it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.93it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.52it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.98it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.88it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.13it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.38it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.48it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.01it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.57it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.02it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.63it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.64it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.13it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 16.95it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.88it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 17.58it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.81it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 18.77it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 19.52it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 20.25it/s]
Batches: 100%|██████████| 1/1 [00:00<00:00, 35.46it/s]
07/23/2025 12:32:43 - INFO - __main__ - Cached embeddings to cache/embeddings_dialogsum_train_combined_all-MiniLM-L6-v2.npy
07/23/2025 12:32:43 - INFO - __main__ - Extracted embeddings shape: (12460, 384)
07/23/2025 12:32:43 - INFO - wandb_logger - Logged embedding information to W&B
07/23/2025 12:32:43 - INFO - __main__ - Performing random selection...
Random selection.
07/23/2025 12:32:43 - INFO - __main__ - Selection completed in 0.00 seconds
07/23/2025 12:32:43 - INFO - __main__ - Selected indices: [ 1702  9258  2966  3452  8086  8855  3148  1881  3030 10283]
07/23/2025 12:32:43 - INFO - wandb_logger - Logged selection results for random to W&B
