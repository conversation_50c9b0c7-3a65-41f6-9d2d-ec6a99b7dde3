{"os": "Linux-6.8.0-60-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.12", "startedAt": "2025-07-25T04:59:40.804489Z", "args": ["--selection_results", "d2pruning/results/selection_results_random_10.json", "--model_name", "meta-llama/Llama-2-7b-hf", "--format_type", "chat", "--num_epochs", "3", "--cache_dir", "d2pruning/cache", "--output_dir", "d2pruning/finetuned_models", "--enable_wandb", "--wandb_run_name", "finetune_random_10"], "program": "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", "codePath": "d2pruning/finetune_llama.py", "codePathLocal": "d2pruning/finetune_llama.py", "email": "<EMAIL>", "root": "/storage/nammt/data_selection_for_assistant_model", "host": "csews-Precision-7920-Tower", "executable": "/storage/nammt/data_selection_for_assistant_model/venv/bin/python", "cpu_count": 6, "cpu_count_logical": 6, "gpu": "NVIDIA RTX A4000", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "33291022336"}, "gpu_nvidia": [{"name": "NVIDIA RTX A4000", "memoryTotal": "17171480576", "cudaCores": 6144, "architecture": "Ampere", "uuid": "GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a"}], "cudaVersion": "12.9", "writerId": "jigojw37ad09pdf9thshghw2bxnums17"}