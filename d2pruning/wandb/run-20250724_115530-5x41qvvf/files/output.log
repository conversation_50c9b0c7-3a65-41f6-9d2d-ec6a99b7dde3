07/24/2025 11:55:33 - INFO - wandb_logger - Initialized W&B run: dialogsum_10samples_instruction_20250724_115529
07/24/2025 11:55:33 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/5x41qvvf
07/24/2025 11:55:33 - INFO - __main__ - ============================================================
07/24/2025 11:55:33 - INFO - __main__ - STEP 1: DATA SELECTION
07/24/2025 11:55:33 - INFO - __main__ - ============================================================
07/24/2025 11:55:33 - INFO - __main__ - Running data selection with method: random
07/24/2025 11:55:33 - INFO - __main__ - Selection results already exist: results/selection_results_random_10.json
07/24/2025 11:55:33 - INFO - __main__ - Running data selection with method: moderate
07/24/2025 11:55:33 - INFO - __main__ - Selection results already exist: results/selection_results_moderate_10.json
07/24/2025 11:55:33 - INFO - __main__ - Running data selection with method: k_center
07/24/2025 11:55:33 - INFO - __main__ - Selection results already exist: results/selection_results_k_center_10.json
07/24/2025 11:55:33 - INFO - __main__ - ============================================================
07/24/2025 11:55:33 - INFO - __main__ - STEP 2: FINE-TUNING
07/24/2025 11:55:33 - INFO - __main__ - ============================================================
07/24/2025 11:55:33 - INFO - __main__ - Running fine-tuning for results/selection_results_random_10.json
07/24/2025 11:55:33 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_random_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_random_10
07/24/2025 11:57:38 - ERROR - __main__ - Fine-tuning failed: 07/24/2025 11:55:45 - INFO - __main__ - Loading selection results from results/selection_results_random_10.json
07/24/2025 11:55:45 - INFO - __main__ - Loaded 10 selected samples using random method
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_115547-3y4c85ee
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_random_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/3y4c85ee
07/24/2025 11:55:48 - INFO - wandb_logger - Initialized W&B run: finetune_random_10
07/24/2025 11:55:48 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/3y4c85ee
07/24/2025 11:55:48 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 11:55:51 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:46<00:46, 46.43s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:10<00:00, 33.55s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:10<00:00, 35.48s/it]
07/24/2025 11:57:03 - INFO - __main__ - Preparing training dataset...

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 14.44 examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 14.11 examples/s]
07/24/2025 11:57:29 - INFO - __main__ - Starting fine-tuning...
07/24/2025 11:57:29 - INFO - wandb_logger - Logged training start for random to W&B
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.

  0%|          | 0/9 [00:00<?, ?it/s]Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 767, in convert_to_tensors
    tensor = as_tensor(value)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 729, in as_tensor
    return torch.tensor(value)
ValueError: too many dimensions 'str'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 241, in train
    trainer.train()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
    return inner_training_loop(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2502, in _inner_training_loop
    batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 5300, in get_batch_samples
    batch_samples.append(next(epoch_iterator))
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
    current_batch = next(dataloader_iter)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
    data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
    return self.collate_fn(data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 46, in __call__
    return self.torch_call(features)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 1014, in torch_call
    batch = pad_without_fast_tokenizer_warning(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 67, in pad_without_fast_tokenizer_warning
    padded = tokenizer.pad(*pad_args, **pad_kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3374, in pad
    return BatchEncoding(batch_outputs, tensor_type=return_tensors)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 240, in __init__
    self.convert_to_tensors(tensor_type=tensor_type, prepend_batch_axis=prepend_batch_axis)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 783, in convert_to_tensors
    raise ValueError(
ValueError: Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' 'truncation=True' to have batched tensors with the same length. Perhaps your features (`text` in this case) have excessive nesting (inputs type `list` where type `int` is expected).

  0%|          | 0/9 [00:00<?, ?it/s]

07/24/2025 11:57:38 - ERROR - __main__ - Failed to fine-tune model for results/selection_results_random_10.json: Fine-tuning failed for random
07/24/2025 11:57:38 - INFO - __main__ - Running fine-tuning for results/selection_results_moderate_10.json
07/24/2025 11:57:38 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_moderate_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_moderate_10
07/24/2025 12:00:00 - ERROR - __main__ - Fine-tuning failed: 07/24/2025 11:58:04 - INFO - __main__ - Loading selection results from results/selection_results_moderate_10.json
07/24/2025 11:58:04 - INFO - __main__ - Loaded 10 selected samples using moderate method
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_115806-rfqcf1yx
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_moderate_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/rfqcf1yx
07/24/2025 11:58:09 - INFO - wandb_logger - Initialized W&B run: finetune_moderate_10
07/24/2025 11:58:09 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/rfqcf1yx
07/24/2025 11:58:09 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 11:58:12 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [01:05<01:05, 65.93s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:12<00:00, 30.72s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:12<00:00, 36.00s/it]
07/24/2025 11:59:25 - INFO - __main__ - Preparing training dataset...

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 13.61 examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 13.49 examples/s]
07/24/2025 11:59:51 - INFO - __main__ - Starting fine-tuning...
07/24/2025 11:59:51 - INFO - wandb_logger - Logged training start for moderate to W&B
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.

  0%|          | 0/9 [00:00<?, ?it/s]Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 767, in convert_to_tensors
    tensor = as_tensor(value)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 729, in as_tensor
    return torch.tensor(value)
ValueError: too many dimensions 'str'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 241, in train
    trainer.train()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
    return inner_training_loop(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2502, in _inner_training_loop
    batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 5300, in get_batch_samples
    batch_samples.append(next(epoch_iterator))
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
    current_batch = next(dataloader_iter)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
    data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
    return self.collate_fn(data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 46, in __call__
    return self.torch_call(features)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 1014, in torch_call
    batch = pad_without_fast_tokenizer_warning(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 67, in pad_without_fast_tokenizer_warning
    padded = tokenizer.pad(*pad_args, **pad_kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3374, in pad
    return BatchEncoding(batch_outputs, tensor_type=return_tensors)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 240, in __init__
    self.convert_to_tensors(tensor_type=tensor_type, prepend_batch_axis=prepend_batch_axis)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 783, in convert_to_tensors
    raise ValueError(
ValueError: Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' 'truncation=True' to have batched tensors with the same length. Perhaps your features (`text` in this case) have excessive nesting (inputs type `list` where type `int` is expected).

  0%|          | 0/9 [00:00<?, ?it/s]

07/24/2025 12:00:00 - ERROR - __main__ - Failed to fine-tune model for results/selection_results_moderate_10.json: Fine-tuning failed for moderate
07/24/2025 12:00:00 - INFO - __main__ - Running fine-tuning for results/selection_results_k_center_10.json
07/24/2025 12:00:00 - INFO - __main__ - Running command: /storage/nammt/data_selection_for_assistant_model/venv/bin/python finetune_llama.py --selection_results results/selection_results_k_center_10.json --model_name meta-llama/Llama-2-7b-hf --format_type instruction --num_epochs 3 --output_dir finetuned_models --cache_dir cache --enable_wandb --wandb_run_name finetune_k_center_10
07/24/2025 12:02:25 - ERROR - __main__ - Fine-tuning failed: 07/24/2025 12:00:26 - INFO - __main__ - Loading selection results from results/selection_results_k_center_10.json
07/24/2025 12:00:26 - INFO - __main__ - Loaded 10 selected samples using k_center method
wandb: Currently logged in as: joshuafoshua (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/d2pruning/wandb/run-20250724_120029-gx3y3d17
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_k_center_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/gx3y3d17
07/24/2025 12:00:31 - INFO - wandb_logger - Initialized W&B run: finetune_k_center_10
07/24/2025 12:00:31 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/runs/gx3y3d17
07/24/2025 12:00:31 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/24/2025 12:00:34 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:52<00:52, 52.98s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:17<00:00, 36.16s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [01:17<00:00, 38.68s/it]
07/24/2025 12:01:53 - INFO - __main__ - Preparing training dataset...

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 17.84 examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 17.35 examples/s]
07/24/2025 12:02:18 - INFO - __main__ - Starting fine-tuning...
07/24/2025 12:02:18 - INFO - wandb_logger - Logged training start for k_center to W&B
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
wandb: WARNING The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.

  0%|          | 0/9 [00:00<?, ?it/s]Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 767, in convert_to_tensors
    tensor = as_tensor(value)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 729, in as_tensor
    return torch.tensor(value)
ValueError: too many dimensions 'str'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 417, in <module>
    main()
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 385, in main
    training_results = finetuner.train(
  File "/storage/nammt/data_selection_for_assistant_model/d2pruning/finetune_llama.py", line 241, in train
    trainer.train()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
    return inner_training_loop(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 2502, in _inner_training_loop
    batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/trainer.py", line 5300, in get_batch_samples
    batch_samples.append(next(epoch_iterator))
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
    current_batch = next(dataloader_iter)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
    data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
    return self.collate_fn(data)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 46, in __call__
    return self.torch_call(features)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 1014, in torch_call
    batch = pad_without_fast_tokenizer_warning(
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/data/data_collator.py", line 67, in pad_without_fast_tokenizer_warning
    padded = tokenizer.pad(*pad_args, **pad_kwargs)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3374, in pad
    return BatchEncoding(batch_outputs, tensor_type=return_tensors)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 240, in __init__
    self.convert_to_tensors(tensor_type=tensor_type, prepend_batch_axis=prepend_batch_axis)
  File "/storage/nammt/data_selection_for_assistant_model/venv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 783, in convert_to_tensors
    raise ValueError(
ValueError: Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' 'truncation=True' to have batched tensors with the same length. Perhaps your features (`text` in this case) have excessive nesting (inputs type `list` where type `int` is expected).

  0%|          | 0/9 [00:00<?, ?it/s]

07/24/2025 12:02:25 - ERROR - __main__ - Failed to fine-tune model for results/selection_results_k_center_10.json: Fine-tuning failed for k_center
07/24/2025 12:02:25 - ERROR - __main__ - Experiment failed: No models were fine-tuned successfully
