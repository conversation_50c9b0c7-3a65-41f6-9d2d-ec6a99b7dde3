07/25/2025 07:29:48 - INFO - __main__ - Logging to ../logs/finetune_20250725_072948.log
07/25/2025 07:29:48 - INFO - __main__ - Loading selection results from d2pruning/results/selection_results_k_center_10.json
wandb: Currently logged in as: joshua<PERSON>shu<PERSON> (joshuafoshua-university-of-engineering-and-technology-hanoi) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: WARNING Using a boolean value for 'reinit' is deprecated. Use 'return_previous' or 'finish_previous' instead.
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_072949-quzso0lt
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run finetune_k_center_10
wandb: ⭐️ View project at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark
wandb: 🚀 View run at https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/quzso0lt
07/25/2025 07:29:50 - INFO - wandb_logger - Initialized W&B run: finetune_k_center_10
07/25/2025 07:29:50 - INFO - wandb_logger - W&B URL: https://wandb.ai/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/runs/quzso0lt
07/25/2025 07:29:50 - INFO - __main__ - W&B logging initialized with run name: finetune_k_center_10
07/25/2025 07:29:50 - INFO - __main__ - Loading model and tokenizer: meta-llama/Llama-2-7b-hf
07/25/2025 07:29:53 - INFO - accelerate.utils.modeling - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:49<00:49, 49.34s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:55<00:00, 23.88s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:55<00:00, 27.70s/it]
07/25/2025 07:30:49 - INFO - __main__ - Preparing training dataset...
trainable params: 16,777,216 || all params: 6,755,192,832 || trainable%: 0.2484

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 568.04 examples/s]
07/25/2025 07:31:12 - INFO - __main__ - Starting fine-tuning...
07/25/2025 07:31:12 - INFO - wandb_logger - Logged training start for k_center to W&B
Using auto half precision backend
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.
***** Running training *****
  Num examples = 10
  Num Epochs = 3
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 4
  Gradient Accumulation steps = 4
  Total optimization steps = 9
  Number of trainable parameters = 16,777,216
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/9 [00:00<?, ?it/s]
 11%|█         | 1/9 [00:02<00:23,  2.96s/it]
                                             

 11%|█         | 1/9 [00:02<00:23,  2.96s/it]
 22%|██▏       | 2/9 [00:04<00:16,  2.40s/it]
                                             

 22%|██▏       | 2/9 [00:04<00:16,  2.40s/it]
 33%|███▎      | 3/9 [00:05<00:10,  1.76s/it]
                                             

 33%|███▎      | 3/9 [00:05<00:10,  1.76s/it]Saving model checkpoint to d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-3
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-3/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-3/special_tokens_map.json

 44%|████▍     | 4/9 [00:11<00:15,  3.12s/it]
                                             

 44%|████▍     | 4/9 [00:11<00:15,  3.12s/it]
 56%|█████▌    | 5/9 [00:13<00:10,  2.73s/it]
                                             

 56%|█████▌    | 5/9 [00:13<00:10,  2.73s/it]
 67%|██████▋   | 6/9 [00:14<00:06,  2.14s/it]
                                             

 67%|██████▋   | 6/9 [00:14<00:06,  2.14s/it]Saving model checkpoint to d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-6
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-6/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-6/special_tokens_map.json

 78%|███████▊  | 7/9 [00:19<00:06,  3.10s/it]
                                             

 78%|███████▊  | 7/9 [00:19<00:06,  3.10s/it]
 89%|████████▉ | 8/9 [00:21<00:02,  2.76s/it]
                                             

 89%|████████▉ | 8/9 [00:21<00:02,  2.76s/it]
100%|██████████| 9/9 [00:22<00:00,  2.19s/it]
                                             

100%|██████████| 9/9 [00:22<00:00,  2.19s/it]Saving model checkpoint to d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-9
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-9/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/k_center_10samples_chat/checkpoint-9/special_tokens_map.json


Training completed. Do not forget to share your model on huggingface.co/models =)



                                             

100%|██████████| 9/9 [00:25<00:00,  2.19s/it]
100%|██████████| 9/9 [00:25<00:00,  2.82s/it]
07/25/2025 07:31:39 - INFO - wandb_logger - Logged training completion for k_center to W&B
Saving model checkpoint to d2pruning/finetuned_models/k_center_10samples_chat
loading configuration file config.json from cache at /home/<USER>/.cache/huggingface/hub/models--meta-llama--Llama-2-7b-hf/snapshots/01c7f73d771dfac7d292323805ebc428287df4f9/config.json
Model config LlamaConfig {
  "architectures": [
    "LlamaForCausalLM"
  ],
  "attention_bias": false,
  "attention_dropout": 0.0,
  "bos_token_id": 1,
  "eos_token_id": 2,
  "head_dim": 128,
  "hidden_act": "silu",
  "hidden_size": 4096,
  "initializer_range": 0.02,
  "intermediate_size": 11008,
  "max_position_embeddings": 4096,
  "mlp_bias": false,
  "model_type": "llama",
  "num_attention_heads": 32,
  "num_hidden_layers": 32,
  "num_key_value_heads": 32,
  "pretraining_tp": 1,
  "rms_norm_eps": 1e-05,
  "rope_scaling": null,
  "rope_theta": 10000.0,
  "tie_word_embeddings": false,
  "torch_dtype": "float16",
  "transformers_version": "4.53.2",
  "use_cache": true,
  "vocab_size": 32000
}

Saving Trainer.data_collator.tokenizer by default as Trainer.processing_class is `None`
tokenizer config file saved in d2pruning/finetuned_models/k_center_10samples_chat/tokenizer_config.json
Special tokens file saved in d2pruning/finetuned_models/k_center_10samples_chat/special_tokens_map.json
07/25/2025 07:31:40 - INFO - __main__ - Fine-tuning completed in 25.73 seconds
