{"time":"2025-07-24T12:17:23.978527534+07:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-24T12:17:24.630011296+07:00","level":"INFO","msg":"stream: created new stream","id":"j7t8qniw"}
{"time":"2025-07-24T12:17:24.630369371+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"j7t8qniw"}
{"time":"2025-07-24T12:17:24.630572279+07:00","level":"INFO","msg":"handler: started","stream_id":"j7t8qniw"}
{"time":"2025-07-24T12:17:24.630594095+07:00","level":"INFO","msg":"sender: started","stream_id":"j7t8qniw"}
{"time":"2025-07-24T12:17:24.631408468+07:00","level":"INFO","msg":"stream: started","id":"j7t8qniw"}
{"time":"2025-07-24T13:48:26.330245557+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/j7t8qniw/file_stream"}
{"time":"2025-07-24T13:48:26.356100114+07:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-data-selection/j7t8qniw/file_stream: {\"error\":\"run dialogsum-data-selection/j7t8qniw not found while streaming file\"}"}
{"time":"2025-07-24T13:54:38.097097601+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T13:54:38.121434207+07:00","level":"ERROR","msg":"runfiles: CreateRunFiles returned error: returned error 404: {\"data\":{\"createRunFiles\":null},\"errors\":[{\"message\":\"run dialogsum-data-selection/j7t8qniw not found during createRunFiles\",\"path\":[\"createRunFiles\"]}]}"}
{"time":"2025-07-24T13:54:38.274847655+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T13:54:39.155695585+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T13:54:39.155871743+07:00","level":"ERROR","msg":"runfiles: CreateRunFiles returned error: returned error 404: {\"data\":{\"createRunFiles\":null},\"errors\":[{\"message\":\"run dialogsum-data-selection/j7t8qniw not found during createRunFiles\",\"path\":[\"createRunFiles\"]}]}"}
{"time":"2025-07-24T13:54:39.336783928+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T13:54:39.699331646+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T13:54:39.866934044+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:54:39.914919975+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T13:54:39.915182092+07:00","level":"ERROR","msg":"runfiles: CreateRunFiles returned error: returned error 404: {\"data\":{\"createRunFiles\":null},\"errors\":[{\"message\":\"run dialogsum-data-selection/j7t8qniw not found during createRunFiles\",\"path\":[\"createRunFiles\"]}]}"}
{"time":"2025-07-24T13:54:40.081134739+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T13:54:40.629097334+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":1.002267624,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T13:54:42.455143785+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:54:47.30896134+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:54:57.266947921+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:55:16.14126539+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:55:40.707678073+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":61.080838631,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T13:55:52.654256252+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:56:40.785767556+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":121.158932735,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T13:56:52.90391443+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:57:40.865550918+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":181.238715465,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T13:57:53.143236508+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:58:40.945014934+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":241.31817915,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T13:58:53.44212442+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T13:59:41.03014829+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":301.403312788,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T13:59:53.685443087+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:00:41.109768954+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":361.482932443,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:00:53.937848481+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:01:41.195536789+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":421.568700957,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:01:54.166139758+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:02:41.284655449+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":481.657819761,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:02:54.406926978+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:03:41.365842683+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":541.739007142,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:03:54.648474498+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:04:41.448381887+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":601.821547201,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:04:54.892316716+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:05:41.527853668+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":661.90101754,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:05:55.135941222+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:06:41.616581803+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":721.989746949,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:06:55.373791085+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:07:41.695287214+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":782.068451676,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:07:55.627473633+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:08:41.78013467+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":842.153298936,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:08:55.870276635+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:09:41.865495581+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":902.238659849,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:09:56.111480915+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:10:41.957912083+07:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"updating run metadata","runtime_seconds":962.331075916,"error_status":"retrying HTTP 409: run j7t8qniw was previously created and deleted; try a new run id"}],"total_operations":1}}
{"time":"2025-07-24T14:10:56.369917433+07:00","level":"INFO","msg":"api: retrying HTTP error","status":409,"url":"https://api.wandb.ai/graphql","body":"{\"errors\":[{\"message\":\"run j7t8qniw was previously created and deleted; try a new run id\",\"path\":[\"upsertBucket\"]}],\"data\":{\"upsertBucket\":null}}"}
{"time":"2025-07-24T14:10:56.370143372+07:00","level":"ERROR","msg":"runupserter: failed to upload changes","error":"api: failed sending: POST https://api.wandb.ai/graphql giving up after 21 attempt(s)"}
{"time":"2025-07-24T14:10:56.672469304+07:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/graphql"}
{"time":"2025-07-24T14:10:56.672610235+07:00","level":"ERROR","msg":"runfiles: CreateRunFiles returned error: returned error 404: {\"data\":{\"createRunFiles\":null},\"errors\":[{\"message\":\"run dialogsum-data-selection/j7t8qniw not found during createRunFiles\",\"path\":[\"createRunFiles\"]}]}"}
{"time":"2025-07-24T14:10:56.673190949+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-07-24T14:10:56.683258311+07:00","level":"INFO","msg":"stream: closing","id":"j7t8qniw"}
{"time":"2025-07-24T14:10:56.683304429+07:00","level":"INFO","msg":"handler: closed","stream_id":"j7t8qniw"}
{"time":"2025-07-24T14:10:56.683329102+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"j7t8qniw"}
{"time":"2025-07-24T14:10:56.683368986+07:00","level":"INFO","msg":"sender: closed","stream_id":"j7t8qniw"}
{"time":"2025-07-24T14:10:56.683480428+07:00","level":"INFO","msg":"stream: closed","id":"j7t8qniw"}
