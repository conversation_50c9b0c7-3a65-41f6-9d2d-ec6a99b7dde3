2025-07-25 01:59:02,971 INFO    MainThread:2983107 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-25 01:59:02,971 INFO    MainThread:2983107 [wandb_setup.py:_flush():80] Configure stats pid to 2983107
2025-07-25 01:59:02,971 INFO    MainThread:2983107 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-25 01:59:02,971 INFO    MainThread:2983107 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/data_selection_for_assistant_model/wandb/settings
2025-07-25 01:59:02,971 INFO    MainThread:2983107 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-25 01:59:02,971 INFO    MainThread:2983107 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_015902-dr5nhkr5/logs/debug.log
2025-07-25 01:59:02,972 INFO    MainThread:2983107 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/data_selection_for_assistant_model/wandb/run-20250725_015902-dr5nhkr5/logs/debug-internal.log
2025-07-25 01:59:02,972 INFO    MainThread:2983107 [wandb_init.py:init():830] calling init triggers
2025-07-25 01:59:02,972 INFO    MainThread:2983107 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'selection_results': 'd2pruning/results/selection_results_random_10.json', 'model_name': 'meta-llama/Llama-2-7b-hf', 'format_type': 'chat', 'num_epochs': 3, 'learning_rate': 0.0002, 'enable_wandb': True, 'wandb_project': 'data-selection-experiments', 'wandb_run_group': None, 'wandb_run_name': 'finetune_random_10', 'seed': 42, 'cache_dir': 'd2pruning/cache', 'output_dir': 'd2pruning/finetuned_models', 'selection_method': 'random', 'num_samples': 10, 'phase': 'fine-tuning', '_wandb': {}}
2025-07-25 01:59:02,972 INFO    MainThread:2983107 [wandb_init.py:init():871] starting backend
2025-07-25 01:59:03,188 INFO    MainThread:2983107 [wandb_init.py:init():874] sending inform_init request
2025-07-25 01:59:03,197 INFO    MainThread:2983107 [wandb_init.py:init():882] backend started and connected
2025-07-25 01:59:03,201 INFO    MainThread:2983107 [wandb_init.py:init():953] updated telemetry
2025-07-25 01:59:03,202 INFO    MainThread:2983107 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-25 01:59:04,077 INFO    MainThread:2983107 [wandb_init.py:init():1029] starting run threads in backend
2025-07-25 01:59:04,363 INFO    MainThread:2983107 [wandb_run.py:_console_start():2458] atexit reg
2025-07-25 01:59:04,363 INFO    MainThread:2983107 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-25 01:59:04,363 INFO    MainThread:2983107 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-25 01:59:04,364 INFO    MainThread:2983107 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-25 01:59:04,367 INFO    MainThread:2983107 [wandb_init.py:init():1075] run started, returning control to user process
